from datetime import datetime
import time
from typing import Optional

from sqlalchemy import Column, types
from sqlmodel import Field, SQLModel
from sqlalchemy.orm import sessionmaker
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy import create_engine

from app.config import settings

# Create engine and session
engine = create_engine(settings.DATABASE_URL)
Session = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()

def get_current_time():
    return datetime.now().isoformat() + "Z"  # Return ISO format string with Z suffix


# Define a custom MEDIUMTEXT type
class MediumText(types.TypeDecorator):
    impl = types.TEXT
    cache_ok = True  # Set cache_ok to True for better performance

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

    def load_dialect_impl(self, dialect):
        if dialect.name == "mysql":
            return dialect.type_descriptor(types.TEXT(length=16777215))
        else:
            return dialect.type_descriptor(types.TEXT())


class WebsiteUrls(SQLModel, table=True):
    __tablename__ = 'website_urls_gemini'
    id: Optional[int] = Field(default=None, primary_key=True)
    scrape_request_ref_id: str = Field(index=True)  # Changed from website_ref to scrape_request_ref_id
    website: str
    url: str = Field(default="", sa_column=Column(MediumText))
    depth: int = 1
    soft_class: str = ""
    hard_class: str = ""  # New field for hard classification results
    priority_url: bool = False
    extracted_text: str = Field(default="", sa_column=Column(MediumText))
    img_url: str = Field(default="", sa_column=Column(MediumText))
    policy: str = Field(default="", sa_column=Column(MediumText))
    registered_name: str = Field(default="", sa_column=Column(MediumText))
    org_id: str = Field(default="default", nullable=True)


class GeneralLogs(SQLModel, table=True):
    __tablename__ = 'general_logs_gemini'
    id: Optional[int] = Field(default=None, primary_key=True)
    analysis_id: int
    timestamp: str
    type: str
    messages: str = Field(sa_column=Column(MediumText))
    response: str = Field(sa_column=Column(MediumText))
    org_id: str = Field(default="default", nullable=True)

# URL Classification models used by Entity Extraction for data retrieval

class MccUrlClassification(SQLModel, table=True):
    __tablename__ = 'mcc_url_classification_gemini'
    id: Optional[int] = Field(default=None, primary_key=True)
    scrape_request_ref_id: str = Field(index=True)
    url: str = Field(sa_column=Column(MediumText))
    classification: str = Field(default="")
    created_at: str = Field(default_factory=get_current_time)
    org_id: str = Field(default="default", nullable=True)


class PolicyUrlClassification(SQLModel, table=True):
    __tablename__ = 'policy_url_classification_gemini'
    id: Optional[int] = Field(default=None, primary_key=True)
    scrape_request_ref_id: str = Field(index=True)
    url: str = Field(sa_column=Column(MediumText))
    classification: str = Field(default="")
    created_at: str = Field(default_factory=get_current_time)
    org_id: str = Field(default="default", nullable=True)

# Policy Analysis model used by Entity Extraction for text retrieval
class PolicyAnalysisNew(SQLModel, table=True):
    """
    Policy Analysis model - stores policy analysis results used by Entity Extraction
    """
    __tablename__ = 'policy_analysis_new_gemini'
    __table_args__ = {'extend_existing': True}

    id: Optional[int] = Field(default=None, primary_key=True)
    website: str
    scrape_request_ref_id: str = Field(index=True)

    # Analysis flow tracking
    analysis_flow_used: Optional[str] = Field(default="normal", nullable=True)  # "normal" or "backup"
    reachability_percentage: Optional[float] = Field(default=None, nullable=True)
    total_urls_processed: Optional[int] = Field(default=0, nullable=True)

    # Policy analysis results
    privacy_policy_url: Optional[str] = Field(default=None, sa_column=Column(MediumText))
    terms_conditions_url: Optional[str] = Field(default=None, sa_column=Column(MediumText))
    shipping_policy_url: Optional[str] = Field(default=None, sa_column=Column(MediumText))
    returns_policy_url: Optional[str] = Field(default=None, sa_column=Column(MediumText))

    # Policy text content (used by Entity Extraction)
    privacy_policy_text: Optional[str] = Field(default=None, sa_column=Column(MediumText))
    terms_conditions_text: Optional[str] = Field(default=None, sa_column=Column(MediumText))
    shipping_policy_text: Optional[str] = Field(default=None, sa_column=Column(MediumText))
    returns_policy_text: Optional[str] = Field(default=None, sa_column=Column(MediumText))

    # Processing status
    processing_status: str = "PENDING"  # PENDING, PROCESSING, COMPLETED, FAILED
    created_at: str = Field(default_factory=get_current_time)
    started_at: Optional[str] = Field(default=None, nullable=True)
    completed_at: Optional[str] = Field(default=None, nullable=True)

    # Error handling
    error_message: Optional[str] = Field(default=None, sa_column=Column(MediumText))
    org_id: str = Field(default="default", nullable=True)


# Entity Extraction Models

class EntityExtractionAnalysis(SQLModel, table=True):
    """
    Main analysis tracking table for entity extraction requests
    """
    __tablename__ = 'entity_extraction_analysis'
    __table_args__ = {'extend_existing': True}

    id: Optional[int] = Field(default=None, primary_key=True)
    scrape_request_ref_id: str = Field(index=True, nullable=False)
    website_url: str = Field(sa_column=Column(MediumText))

    # Processing status
    processing_status: str = "PENDING"  # PENDING, PROCESSING, COMPLETED, FAILED

    # Entity extraction results
    legal_name: Optional[str] = Field(default=None, sa_column=Column(MediumText))
    business_email: Optional[str] = Field(default=None, sa_column=Column(MediumText))
    support_email: Optional[str] = Field(default=None, sa_column=Column(MediumText))
    business_contact_numbers: Optional[str] = Field(default=None, sa_column=Column(MediumText))
    business_location: Optional[str] = Field(default=None, sa_column=Column(MediumText))

    # Policy analysis results
    has_jurisdiction_law: Optional[str] = Field(default=None, nullable=True)
    jurisdiction_details: Optional[str] = Field(default=None, sa_column=Column(MediumText))
    accepts_international_orders: Optional[str] = Field(default=None, nullable=True)
    shipping_policy_details: Optional[str] = Field(default=None, sa_column=Column(MediumText))
    jurisdiction_place: Optional[str] = Field(default=None, sa_column=Column(MediumText))
    shipping_countries: Optional[str] = Field(default=None, sa_column=Column(MediumText))

    # Policy text content
    privacy_policy_text: Optional[str] = Field(default=None, sa_column=Column(MediumText))
    terms_conditions_text: Optional[str] = Field(default=None, sa_column=Column(MediumText))

    # URL reachability status
    urls_reachable_by_gemini: Optional[str] = Field(default=None, sa_column=Column(MediumText))  # JSON list
    urls_not_reachable_by_gemini: Optional[str] = Field(default=None, sa_column=Column(MediumText))  # JSON list

    # Processing method used
    extraction_method: Optional[str] = Field(default=None)  # "gemini", "openai", "mixed"

    # URL processing statistics
    total_urls_processed: Optional[int] = Field(default=None, nullable=True)
    all_urls_found: Optional[str] = Field(default=None, sa_column=Column(MediumText))  # JSON list
    reachable_urls: Optional[str] = Field(default=None, sa_column=Column(MediumText))  # JSON list
    unreachable_urls: Optional[str] = Field(default=None, sa_column=Column(MediumText))  # JSON list
    policy_urls_matched: Optional[str] = Field(default=None, sa_column=Column(MediumText))  # JSON object

    # Timestamps
    created_at: str = Field(default_factory=get_current_time)
    started_at: Optional[str] = Field(default=None, nullable=True)
    completed_at: Optional[str] = Field(default=None, nullable=True)

    # Error handling
    error_message: Optional[str] = Field(default=None, sa_column=Column(MediumText))

    # Organization tracking
    org_id: str = Field(default="default", nullable=False, index=True)


class EntityExtractionUrlAnalysis(SQLModel, table=True):
    """
    Entity Extraction URL Analysis model - stores individual URL analysis results
    """
    __tablename__ = 'entity_extraction_url_analysis'
    __table_args__ = {'extend_existing': True}

    id: Optional[int] = Field(default=None, primary_key=True)
    analysis_id: int = Field(index=True)  # References EntityExtractionAnalysis.id
    entity_analysis_id: int = Field(index=True)  # Additional reference field in DB
    scrape_request_ref_id: str = Field(index=True, nullable=False)

    # URL information
    url: Optional[str] = Field(default=None, sa_column=Column(MediumText))
    url_type: str = Field(default="")
    url_classification: Optional[str] = Field(default="")
    url_source_table: Optional[str] = Field(default="")
    is_reachable_by_gemini: Optional[bool] = Field(default=None, nullable=True)

    # Classification fields
    soft_classification: Optional[str] = Field(default=None, sa_column=Column(MediumText))
    hard_classification: Optional[str] = Field(default=None, sa_column=Column(MediumText))

    # Extracted content
    extracted_text: Optional[str] = Field(default=None, sa_column=Column(MediumText))
    extracted_text_length: Optional[int] = Field(default=0)
    extracted_entities: Optional[str] = Field(default=None, sa_column=Column(MediumText))  # JSON object

    # Processing details
    processing_method: Optional[str] = Field(default=None)
    processing_status: str = Field(default="PENDING")  # PENDING, COMPLETED, FAILED, SKIPPED
    error_message: Optional[str] = Field(default=None, sa_column=Column(MediumText))

    # Timestamps
    created_at: str = Field(default_factory=get_current_time)
    processed_at: Optional[str] = Field(default=None, nullable=True)

    org_id: str = Field(default="default", nullable=False)

