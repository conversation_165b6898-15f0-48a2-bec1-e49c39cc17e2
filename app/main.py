from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from app.database import init_db
from app.Extractor.routers.entity_extraction_router import router as entity_router
from app.Extractor.database_setup import create_entity_extractor_tables
import asyncio
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Initialize the FastAPI app
app = FastAPI(title="Entity Extraction API", version="1.0", description="API for extracting business entities from website content")

# Configure CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure as needed for your environment
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include Entity Extraction router
app.include_router(entity_router)

# Initialize database
@app.on_event("startup")
def on_startup():
    logger.info("Initializing Entity Extraction API")
    try:
        init_db()
        logger.info("Database initialized successfully")

        # Initialize Entity Extractor tables
        create_entity_extractor_tables()
        logger.info("Entity Extractor tables initialized successfully")
    except Exception as e:
        logger.error(f"Error initializing database: {e}")
        # Don't raise here - just log the error so we can still start the app
        # and serve the health check endpoints, etc.

@app.on_event("shutdown")
async def shutdown_event():
    logger.info("Entity Extraction API shutting down")
    pass

@app.middleware("http")
async def log_request_body(request: Request, call_next):
    body = await request.body()
    logger.debug(f"Raw Request Body: {body.decode('utf-8')}")
    request = Request(request.scope, asyncio.StreamReader())
    request._receive = lambda: {"type": "http.request", "body": body}
    return await call_next(request)

# Health check endpoint
@app.get("/health")
async def health_check():
    return {"status": "ok", "message": "Entity Extraction API is running"}


