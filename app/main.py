from contextlib import asynccontextmanager
from fastapi import FastAP<PERSON>, Request
from fastapi.middleware.cors import CORSMiddleware
from app.database import init_db
from app.Extractor.routers.entity_extraction_router import router as entity_router
from app.Extractor.database_setup import create_entity_extractor_tables
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    logger.info("Initializing Entity Extraction API")
    try:
        init_db()
        logger.info("Database initialized successfully")

        # Initialize Entity Extractor tables
        create_entity_extractor_tables()
        logger.info("Entity Extractor tables initialized successfully")
    except Exception as e:
        logger.error(f"Error initializing database: {e}")
        # Don't raise here - just log the error so we can still start the app
        # and serve the health check endpoints, etc.

    yield

    # Shutdown
    logger.info("Entity Extraction API shutting down")


# Initialize the FastAPI app with lifespan
app = FastAPI(
    title="Entity Extraction API",
    version="1.0",
    description="API for extracting business entities from website content",
    lifespan=lifespan
)

# Configure CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure as needed for your environment
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include Entity Extraction router
app.include_router(entity_router)


@app.middleware("http")
async def log_request_info(request: Request, call_next):
    """Log basic request information without consuming the body"""
    logger.debug(f"Request: {request.method} {request.url}")
    response = await call_next(request)
    logger.debug(f"Response status: {response.status_code}")
    return response

# Health check endpoint
@app.get("/health")
async def health_check():
    return {"status": "ok", "message": "Entity Extraction API is running"}


