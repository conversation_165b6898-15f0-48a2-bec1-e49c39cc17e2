"""
Database Setup for Entity Extractor

Script to create the necessary database tables for the Entity Extractor tool
"""

from sqlmodel import SQLModel
from app.database import engine
from app.models.db_models import EntityExtractionAnalysis, EntityExtractionUrlAnalysis


def create_entity_extractor_tables():
    """
    Create database tables for Entity Extractor
    """
    try:
        print("Creating Entity Extractor database tables...")
        
        # Create all tables defined in the models
        SQLModel.metadata.create_all(engine)
        
        print("✅ Entity Extractor database tables created successfully!")
        print("Tables created:")
        print("  - entity_extraction_analysis")
        print("  - entity_extraction_url_analysis")
        
    except Exception as e:
        print(f"❌ Error creating Entity Extractor database tables: {str(e)}")
        raise


if __name__ == "__main__":
    create_entity_extractor_tables()
