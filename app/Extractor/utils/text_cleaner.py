import re

def clean_policy_text(text: str) -> str:
    """
    Deduplicates repeated lines/paragraphs and strips out
    obvious navigation cruft (e.g. "Menu Toggle", "Search for:", "Skip to content").
    Falls back to original text if cleaning removes everything.
    """
    # 1) Split into lines, trim whitespace and drop empties
    lines = [line.strip() for line in text.splitlines() if line.strip()]

    # 2) Remove exact duplicates (preserving first occurrence)
    seen = set()
    unique = []
    for line in lines:
        if line in seen:
            continue
        seen.add(line)
        unique.append(line)

    # 3) Filter out nav-only lines (exact matches)
    nav_only = re.compile(r'^(Menu Toggle|Search for:|Skip to content|HomeShop Menu Toggle)$', re.IGNORECASE)
    filtered = [l for l in unique if not nav_only.match(l)]

    # 4) If filtering removed all lines, restore the de-duplicated content
    if not filtered:
        filtered = unique

    # 5) Re-join into paragraphs (two newlines between logical breaks)
    return "\n\n".join(filtered)
