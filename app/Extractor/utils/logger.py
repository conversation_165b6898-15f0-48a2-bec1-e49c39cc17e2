import json
import traceback
from datetime import datetime
from typing import Any, Dict, Optional, Union

from app.database import get_session
from app.models.db_models import GeneralLogs


class EntityExtractorLogger:
    """
    Specialized logger for Entity Extraction Tool that logs to GeneralLogs table
    """
    
    def __init__(self, analysis_id: Union[int, str], scrape_request_id: Optional[str] = None):
        self.analysis_id = analysis_id
        self.scrape_request_id = scrape_request_id
        self.tool_name = "entity_extractor"

    def _format_message(
        self, level: str, message: str, data: Optional[Dict[str, Any]] = None
    ) -> str:
        """Format message for console output"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        prefix = f"[{timestamp}][EntityExtractor][{self.analysis_id}][{self.scrape_request_id or 'NO_REF'}]"
        if data:
            try:
                data_str = "\n" + json.dumps(data, indent=2, default=str)
            except:
                data_str = f"\n{str(data)}"
        else:
            data_str = ""
        return f"{prefix} {level}: {message}{data_str}"

    def _log_to_database(self, level: str, message: str, data: Optional[Dict[str, Any]] = None, response: str = ""):
        """Log to GeneralLogs table with tool_name as type"""
        try:
            with next(get_session()) as session:
                # Prepare messages field with structured data
                log_data = {
                    "level": level,
                    "message": message,
                    "scrape_request_ref_id": self.scrape_request_id,
                    "timestamp": datetime.now().isoformat(),
                    "data": data or {}
                }
                
                log_entry = GeneralLogs(
                    analysis_id=int(self.analysis_id) if isinstance(self.analysis_id, (int, str)) and str(self.analysis_id).isdigit() else 0,
                    timestamp=datetime.now().isoformat(),
                    type=self.tool_name,  # Using tool_name as type as per user requirement
                    messages=json.dumps(log_data, default=str),
                    response=response,
                    org_id="default"
                )
                
                session.add(log_entry)
                session.commit()
                
        except Exception as db_error:
            # Fallback to console if database logging fails
            print(f"Failed to log to database: {str(db_error)}")

    def info(self, message: str, data: Optional[Dict[str, Any]] = None):
        """Log info message"""
        formatted_message = self._format_message("INFO", message, data)
        print(formatted_message)
        self._log_to_database("INFO", message, data)

    def error(
        self, message: str, error: Optional[Exception] = None, data: Optional[Dict[str, Any]] = None
    ):
        """Log error message"""
        error_data = data or {}
        if error:
            error_data["error"] = str(error)
            error_data["traceback"] = traceback.format_exc()
        
        formatted_message = self._format_message("ERROR", message, error_data)
        print(formatted_message)
        
        # Log traceback separately for better readability
        if error:
            print(f"Traceback for analysis {self.analysis_id}:")
            traceback.print_exception(type(error), error, error.__traceback__)
        
        self._log_to_database("ERROR", message, error_data)

    def debug(self, message: str, data: Optional[Dict[str, Any]] = None):
        """Log debug message"""
        formatted_message = self._format_message("DEBUG", message, data)
        print(formatted_message)
        self._log_to_database("DEBUG", message, data)

    def warning(self, message: str, data: Optional[Dict[str, Any]] = None):
        """Log warning message"""
        formatted_message = self._format_message("WARNING", message, data)
        print(formatted_message)
        self._log_to_database("WARNING", message, data)

    def critical(self, message: str, data: Optional[Dict[str, Any]] = None):
        """Log critical message"""
        formatted_message = self._format_message("CRITICAL", message, data)
        print(formatted_message)
        self._log_to_database("CRITICAL", message, data)

    def log_extraction_result(self, url: str, entities: Dict[str, Any], method: str):
        """Log entity extraction results"""
        data = {
            "url": url,
            "extraction_method": method,
            "entities_found": entities
        }
        self.info(f"Entity extraction completed for {url}", data)

    def log_policy_analysis(self, url: str, analysis_type: str, result: Dict[str, Any], method: str):
        """Log policy analysis results"""
        data = {
            "url": url,
            "analysis_type": analysis_type,
            "method": method,
            "result": result
        }
        self.info(f"Policy analysis completed: {analysis_type}", data)

    def log_reachability_check(self, urls_reachable: list, urls_unreachable: list):
        """Log URL reachability check results"""
        data = {
            "reachable_count": len(urls_reachable),
            "unreachable_count": len(urls_unreachable),
            "reachable_urls": urls_reachable,
            "unreachable_urls": urls_unreachable
        }
        self.info("URL reachability check completed", data)
