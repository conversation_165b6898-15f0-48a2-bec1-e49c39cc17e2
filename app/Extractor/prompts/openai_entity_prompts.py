"""
OpenAI-specific prompts for Entity Extraction

This module contains prompts optimized for OpenAI GPT models
"""


class OpenAIEntityPrompts:
    """
    Prompts specifically designed for OpenAI GPT models
    """
    @staticmethod
    def get_entity_extraction_text_prompt(url: str, text_content: str, url_type: str = None) -> str:

        return f"""
You are an expert business analyst who has a lot of experience in dealing with business's official website and the detailed information present in websites URLs. 
You will be provided with text present in either of website's terms and condition OR privacy policy OR shipping policy OR returns policy OR refund policy urls OR and you need to extract specific business information from this text.

Source URL: {url}

* Input Starts *
Text Content:
{text_content}
* Input Ends *


* Tasks Starts *
You will be provided with text present in either of website's home page, terms and condition OR privacy policy OR shipping policy OR returns policy OR refund policy urls scraped text and you need to extract specific business information from this text.
Your main task is to extract the following information from the Text Content.

1. "legal_name" ---> Legal Name of the Company: a string, The official registered business name (single value only)
2. "business_email" ---> Business Official Email Address: a list [], Primary business contact email/emails as array (contact@, info@, etc.)
3. "support_email" ---> Support Emails: a list [], Customer support email/emails addresses as array (support@, help@, customerservice@, etc.)
4. "business_contact_numbers" ---> Business Official Contact Numbers: a list[],  Official phone number/numbers for business inquiries as array, extract as it is
5. "business_location" ---> Business Location: a list [] for addresses, Complete business addresses as array (include all locations if multiple official address is present)
6. "accepts_international_orders" ---> International Shipping Offered : a string, yes or no or not_mentioned, Does the company accept international orders/shipping? (yes/no/not_mentioned). It is assumed that website offered services to Indin. Any country outside India is assumed be international.
7. "shipping_countries" ---> Shipping Countries : a list [], a list of destination where the company offers its shipping services, should be specifically mentioned in the policies
8. "shipping_policy_details" ---> Shipping Policy Details: a string, Summary of phrases or snippets which were used to conclude the answer on international shipping details, (not more than 10-20 words)
9. "has_jurisdiction_law" ---> Jurisdiction/Governing Law: a string, yes/no, Does the website specify governing law or jurisdiction in case of any dispute (yes/no)
10. "jurisdiction_place" ---> Jurisdiction/Governing Law location : a list [], this should have the places of which the governing law applies to any disputes
11. "jurisdiction_details" ---> Jurisdiction/Governing Law details : a string, summary of snippet which was used to specify the jurisdiction/law related query above, in not more than 10-25 words.

**Instructions:**

- Go through all the text and only derive mentioned in the websites, for e.g. do not assume anything, do not read reviews mentioned, or comments in the websites mentioend. 
- Extract only verified information that appears on the website
- If a piece of information is not found, return empty string or list for that field
- Be precise and avoid making assumptions

**Output Format:**
Return your response strictly in JSON format with below 11 keys:

```json
{{
    "legal_name": "Single company legal name or null", a string, provide "" empty string in case not identified.
    "business_email": a list, ["<EMAIL>", "<EMAIL>"], provide empty [] list in case not identified,
    "support_email": a list, ["<EMAIL>", "<EMAIL>"], provide empty [] list in case not identified,
    "business_contact_numbers": a list, ["+1234567890", "+0987654321"], provide empty [] list in case not identified,
    "business_location": a list, ["Address 1", "Address 2"], provide empty [] list in case not identified,
    "accepts_international_orders": "yes/no", a string
    "shipping_countries":["India","Australia"....], a list of destination where the company offers its shipping services, should be specifically mentioned in the policies, provide empty [] list in case not identified,
    "shipping_policy_details": "Summary of shipping and delivery policies or null", a string.
    "has_jurisdiction_law": "yes/no", Only yes and no, a string.
    "jurisdiction_place": a list [], this should have the places of which the governing law applies to any disputes, provide empty [] list in case not identified,
    "jurisdiction_details" : Jurisdiction/Governing Law details : a string, summary of snippet which was used to specify the jurisdiction/lar related query above, in not more than 10-15 words.
    }}

```

Please ensure accuracy and only extract information that is clearly visible on the website pages.
"""



