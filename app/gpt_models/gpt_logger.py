import json
import os
import time
from datetime import datetime, timedelta
import uuid
from app.database import get_session
from app.models.db_models import GeneralLogs
from app.utils.logger import ConsoleLogger

class APIResponseLogger:
    """Logger for storing API responses in JSON format locally"""
    
    def __init__(self, log_dir="api_logs"):
        self.log_dir = log_dir
        os.makedirs(log_dir, exist_ok=True)
    
    def log_response(self, api_type, request_data, response_data, metadata=None):
        """Store API response in JSON format"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            request_id = str(uuid.uuid4())[:8]
            filename = f"{api_type}_{timestamp}_{request_id}.json"
            filepath = os.path.join(self.log_dir, filename)
            
            log_entry = {
                "timestamp": datetime.now().isoformat(),
                "api_type": api_type,
                "request_id": request_id,
                "request_data": request_data,
                "response_data": response_data,
                "metadata": metadata or {}
            }
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(log_entry, f, indent=2, ensure_ascii=False)
                
            print(f"API response logged to: {filepath}")
            return filepath
            
        except Exception as e:
            print(f"Error logging API response: {str(e)}")
            return None

# Global logger instance
api_response_logger = APIResponseLogger()

class GeminiApiLogger:
    """Utility class for logging Gemini API calls and responses"""
    
    # Cost estimates per 1k tokens (adjust these values as needed)
    COST_PER_1K_INPUT_TOKENS = 0.00035  # $0.00035 per 1K input tokens
    COST_PER_1K_OUTPUT_TOKENS = 0.00105  # $0.00105 per 1K output tokens
    
    @staticmethod
    def log_api_call(
        task_type,
        model_name,
        input_text,
        output_text,
        usage_metadata,
        website_url=None,
        org_id="default",
        request_id=None,
        http_status=200,
        error_message=None,
        is_cached=False
    ):
        try:
            # Create logger instance
            logger = ConsoleLogger("gemini_api_logger")
            
            # Generate request ID if not provided
            if not request_id:
                request_id = str(uuid.uuid4())
                
            # Extract token counts
            prompt_tokens = 0
            completion_tokens = 0
            total_tokens = 0
            cached_tokens = 0
            
            # Parse usage metadata
            try:
                logger.info(f"Usage metadata: {str(usage_metadata)}")
                
                # Convert usage_metadata to a dictionary if it's not already
                if usage_metadata is None:
                    metadata_dict = {}
                elif isinstance(usage_metadata, str):
                    # Try to parse from string
                    metadata_str = usage_metadata
                    # Convert to dict if it's a string representation of a dict
                    if metadata_str.startswith('{') and metadata_str.endswith('}'):
                        try:
                            metadata_dict = json.loads(metadata_str)
                        except:
                            metadata_dict = {}
                    else:
                        metadata_dict = {}
                        for item in metadata_str.split(' '):
                            if '=' in item:
                                key, value = item.split('=', 1)
                                metadata_dict[key] = value
                elif hasattr(usage_metadata, 'get'):
                    # Already a dict or similar
                    metadata_dict = usage_metadata
                elif hasattr(usage_metadata, '__dict__'):
                    # It's an object with attributes
                    metadata_dict = usage_metadata.__dict__
                else:
                    # Convert to string and then dictionary
                    metadata_dict = {'raw_data': str(usage_metadata)}
                
                # Extract token counts from metadata
                prompt_tokens = GeminiApiLogger.extract_value(metadata_dict, 'prompt_token_count', 0)
                completion_tokens = GeminiApiLogger.extract_value(metadata_dict, 'candidates_token_count', 0)
                total_tokens = GeminiApiLogger.extract_value(metadata_dict, 'total_token_count', 0)
                cached_tokens = GeminiApiLogger.extract_value(metadata_dict, 'cached_content_token_count', 0)
                
                # Method 2: Look for nested attributes in common paths
                if prompt_tokens == 0:
                    for path in ['prompt_tokens_details', 'tokens_details', 'prompt']:
                        tokens = GeminiApiLogger.extract_nested_value(metadata_dict, path)
                        if tokens > 0:
                            prompt_tokens = tokens
                            break
                
                if completion_tokens == 0:
                    for path in ['completion_tokens_details', 'candidates_tokens_details', 'completion']:
                        tokens = GeminiApiLogger.extract_nested_value(metadata_dict, path)
                        if tokens > 0:
                            completion_tokens = tokens
                            break
                
                # If we still don't have a value for total_tokens, sum the prompt and completion counts
                if total_tokens == 0 and prompt_tokens > 0 and completion_tokens > 0:
                    total_tokens = prompt_tokens + completion_tokens
                
                # Attempt to extract from token count directly if possible
                if total_tokens == 0 and hasattr(usage_metadata, 'token_count'):
                    total_tokens = usage_metadata.token_count
                
                # As a fallback, count the input and output text
                if total_tokens == 0:
                    # Estimate token count based on text length (very rough estimate)
                    input_words = len(input_text.split()) if input_text else 0
                    output_words = len(output_text.split()) if output_text else 0
                    prompt_tokens = input_words * 1.3  # Rough estimate: 1.3 tokens per word
                    completion_tokens = output_words * 1.3
                    total_tokens = prompt_tokens + completion_tokens
                
            except Exception as e:
                logger.error(f"Error parsing usage metadata: {str(e)}")
                # Use default values if parsing fails
            
            # Calculate estimated cost
            input_cost = (prompt_tokens / 1000.0) * GeminiApiLogger.COST_PER_1K_INPUT_TOKENS
            output_cost = (completion_tokens / 1000.0) * GeminiApiLogger.COST_PER_1K_OUTPUT_TOKENS
            cost_estimate = input_cost + output_cost
            
            # Create database session
            session = next(get_session())
            
            try:
                # Create log entry
                now = datetime.now().isoformat() + "Z"
                
                # Ensure all token counts are integers with safe conversion
                try:
                    prompt_token_int = max(0, int(prompt_tokens)) if prompt_tokens is not None else 0
                except (ValueError, TypeError):
                    logger.warning(f"Invalid prompt_tokens value: {prompt_tokens}, defaulting to 0")
                    prompt_token_int = 0
                    
                try:
                    completion_token_int = max(0, int(completion_tokens)) if completion_tokens is not None else 0
                except (ValueError, TypeError):
                    logger.warning(f"Invalid completion_tokens value: {completion_tokens}, defaulting to 0")
                    completion_token_int = 0
                    
                try:
                    total_token_int = max(0, int(total_tokens)) if total_tokens is not None else 0
                except (ValueError, TypeError):
                    logger.warning(f"Invalid total_tokens value: {total_tokens}, defaulting to 0")
                    total_token_int = 0
                    
                try:
                    cached_token_int = max(0, int(cached_tokens)) if cached_tokens is not None else 0
                except (ValueError, TypeError):
                    logger.warning(f"Invalid cached_tokens value: {cached_tokens}, defaulting to 0")
                    cached_token_int = 0
                
                # Create a simplified log entry using GeneralLogs
                log_data = {
                    "request_id": request_id,
                    "model_name": model_name,
                    "task_type": task_type,
                    "prompt_tokens": prompt_token_int,
                    "completion_tokens": completion_token_int,
                    "total_tokens": total_token_int,
                    "cached_tokens": cached_token_int,
                    "cost_estimate": cost_estimate,
                    "website_url": website_url,
                    "http_status": http_status,
                    "error_message": error_message,
                    "processing_time_ms": 0,
                    "is_cached": is_cached,
                    "usage_metadata": str(usage_metadata)[:1000] if usage_metadata else ""
                }

                log_entry = GeneralLogs(
                    analysis_id=0,  # Default analysis_id for API logs
                    timestamp=now,
                    type="gemini_api_call",
                    messages=input_text[:10000] if input_text else "",  # Truncate for storage
                    response=json.dumps(log_data),
                    org_id=org_id
                )
                
                # Add and commit to database
                session.add(log_entry)
                session.commit()
                
                logger.info(f"Logged Gemini API call: {task_type} for {website_url or 'unknown'}, tokens: {total_token_int}")
                return log_entry
                
            except Exception as db_error:
                logger.error(f"Error logging Gemini API call to database: {str(db_error)}")
                session.rollback()
                return None
            finally:
                session.close()
                
        except Exception as e:
            print(f"Error in GeminiApiLogger.log_api_call: {str(e)}")
            return None
    
    @staticmethod
    def extract_value(metadata_dict, key, default_value=0):
        """Extract a value from metadata dictionary with type conversion"""
        if not metadata_dict:
            return default_value
            
        value = metadata_dict.get(key, default_value)
        
        # Try to convert to int if it's a string
        if isinstance(value, str):
            try:
                return int(value)
            except (ValueError, TypeError):
                pass
                
        # If it's already a number or conversion failed, return as is
        return value
    
    @staticmethod
    def extract_nested_value(metadata_dict, path_key, default_value=0):
        """Extract nested values from metadata dictionary"""
        # First try direct access
        value = GeminiApiLogger.extract_value(metadata_dict, path_key, None)
        if value is not None:
            return value
            
        # Try to access as a list of dicts
        if path_key in metadata_dict and isinstance(metadata_dict[path_key], list):
            items = metadata_dict[path_key]
            total = 0
            for item in items:
                if isinstance(item, dict) and 'token_count' in item:
                    total += GeminiApiLogger.extract_value(item, 'token_count', 0)
            if total > 0:
                return total
        
        # Look for key_count pattern
        count_key = f"{path_key}_count"
        value = GeminiApiLogger.extract_value(metadata_dict, count_key, None)
        if value is not None:
            return value
            
        return default_value
            
    @staticmethod
    def start_timer():
        """Start a timer for measuring API call duration"""
        return time.time()
        
    @staticmethod
    def calculate_duration_ms(start_time):
        """Calculate duration in milliseconds from a start time"""
        return int((time.time() - start_time) * 1000)
            
    @staticmethod
    def get_usage_summary(days=30, org_id=None):
        """Get usage summary for the specified period"""
        try:
            session = next(get_session())
            
            try:
                # Get current time
                now = datetime.now()

                # Calculate the date from days ago using timedelta
                from_date = now - timedelta(days=days)
                from_date_iso = from_date.isoformat() + "Z"

                # Simplified query using GeneralLogs
                # Note: This is a simplified implementation since we changed the logging structure
                from sqlmodel import select
                query = select(GeneralLogs).where(
                    GeneralLogs.type == "gemini_api_call",
                    GeneralLogs.timestamp >= from_date_iso
                )

                if org_id:
                    query = query.where(GeneralLogs.org_id == org_id)

                logs = session.exec(query).all()
                
                # Aggregate data from GeneralLogs (simplified)
                total_calls = len(logs)
                total_tokens = 0
                total_prompt_tokens = 0
                total_completion_tokens = 0
                total_cost = 0

                # Group by task type
                task_type_summary = {}
                for log in logs:
                    try:
                        # Parse the response JSON to get the original data
                        log_data = json.loads(log.response) if log.response else {}
                        task_type = log_data.get("task_type", "unknown")
                        tokens = log_data.get("total_tokens", 0)
                        cost = log_data.get("cost_estimate", 0)

                        total_tokens += tokens
                        total_prompt_tokens += log_data.get("prompt_tokens", 0)
                        total_completion_tokens += log_data.get("completion_tokens", 0)
                        total_cost += cost

                        if task_type not in task_type_summary:
                            task_type_summary[task_type] = {
                                "calls": 0,
                                "tokens": 0,
                                "cost": 0
                            }
                        task_type_summary[task_type]["calls"] += 1
                        task_type_summary[task_type]["tokens"] += tokens
                        task_type_summary[task_type]["cost"] += cost
                    except (json.JSONDecodeError, KeyError):
                        # Skip malformed log entries
                        continue
                
                return {
                    "period_days": days,
                    "from_date": from_date.isoformat(),
                    "to_date": now.isoformat(),
                    "total_calls": total_calls,
                    "total_tokens": total_tokens,
                    "total_prompt_tokens": total_prompt_tokens,
                    "total_completion_tokens": total_completion_tokens,
                    "total_cost_usd": round(total_cost, 6),
                    "by_task_type": task_type_summary
                }
                
            except Exception as query_error:
                logger = ConsoleLogger("gemini_api_logger")
                logger.error(f"Error querying Gemini API logs: {str(query_error)}")
                return {
                    "error": str(query_error),
                    "period_days": days
                }
            finally:
                session.close()
                
        except Exception as e:
            logger = ConsoleLogger("gemini_api_logger")
            logger.error(f"Error in GeminiApiLogger.get_usage_summary: {str(e)}")
            return {
                "error": str(e),
                "period_days": days
            }

def log_gemini_usage(
    model_name,
    task_type,
    input_text,
    output_text,
    usage_metadata,
    website_url=None,
    org_id="default",
    request_id=None,
    http_status=200,
    error_message=None,
    is_cached=False
):
    """Convenience function for logging Gemini usage"""
    return GeminiApiLogger.log_api_call(
        task_type=task_type,
        model_name=model_name,
        input_text=input_text,
        output_text=output_text,
        usage_metadata=usage_metadata,
        website_url=website_url,
        org_id=org_id,
        request_id=request_id,
        http_status=http_status,
        error_message=error_message,
        is_cached=is_cached
    ) 
