"""
Test cases for the Advanced Entity Merger

This module contains comprehensive tests for the enhanced merge logic
that combines multiple AI extraction results.
"""

import pytest
from app.Extractor.services.advanced_entity_merger import AdvancedEntityMerger


class TestAdvancedEntityMerger:
    """Test cases for AdvancedEntityMerger"""
    
    def setup_method(self):
        """Setup test fixtures"""
        self.merger = AdvancedEntityMerger()
    
    def test_merge_multiple_results_basic(self):
        """Test basic merging of multiple AI results"""
        results = [
            {
                "data": {
                    "legal_name": "Acme Corporation Ltd",
                    "business_email": ["<EMAIL>"],
                    "has_jurisdiction_law": "yes"
                },
                "source": "gemini",
                "source_index": 0
            },
            {
                "data": {
                    "legal_name": "ACME CORP LTD",
                    "business_email": ["<EMAIL>", "<EMAIL>"],
                    "support_email": ["<EMAIL>"],
                    "has_jurisdiction_law": "no"
                },
                "source": "openai",
                "source_index": 0
            }
        ]
        
        merged = self.merger.merge_multiple_results(results)
        
        # Check that emails are properly merged and deduplicated
        assert "<EMAIL>" in merged["business_email"]
        assert "<EMAIL>" in merged["business_email"]
        assert len(merged["business_email"]) == 2
        
        # Check that support email is included
        assert merged["support_email"] == ["<EMAIL>"]
        
        # Check that jurisdiction "yes" wins over "no"
        assert merged["has_jurisdiction_law"] == "yes"
        
        # Check that legal name is properly selected
        assert merged["legal_name"] in ["Acme Corporation Ltd", "ACME CORP LTD"]
    
    def test_priority_based_merging(self):
        """Test priority-based merging for different sources"""
        # Test that higher priority sources are preferred
        results = [
            {
                "data": {"legal_name": "Lower Priority Corp"},
                "source": "openai",
                "source_index": 0
            },
            {
                "data": {"legal_name": "Higher Priority Corp"},
                "source": "gemini",
                "source_index": 0
            }
        ]

        # Temporarily set different priorities
        self.merger.source_priority["gemini"] = 2
        self.merger.source_priority["openai"] = 1

        merged = self.merger.merge_multiple_results(results)
        assert merged["legal_name"] == "Higher Priority Corp"

        # Reset priorities
        self.merger.source_priority["gemini"] = 1
        self.merger.source_priority["openai"] = 1
    
    def test_garbage_value_detection(self):
        """Test enhanced garbage value detection"""
        # Test obvious garbage values
        assert self.merger._is_garbage_value(None)
        assert self.merger._is_garbage_value("")
        assert self.merger._is_garbage_value("   ")
        assert self.merger._is_garbage_value("not_applicable")
        assert self.merger._is_garbage_value("N/A")
        assert self.merger._is_garbage_value("not found")
        assert self.merger._is_garbage_value("information not available")
        
        # Test valid values
        assert not self.merger._is_garbage_value("Acme Corp")
        assert not self.merger._is_garbage_value(["<EMAIL>"])
        assert not self.merger._is_garbage_value("yes")
        
        # Test empty lists
        assert self.merger._is_garbage_value([])
        assert self.merger._is_garbage_value(["", None, "n/a"])
        
        # Test valid lists
        assert not self.merger._is_garbage_value(["<EMAIL>"])
    
    def test_list_field_merging(self):
        """Test intelligent list field merging"""
        value_list = [
            {
                "value": ["<EMAIL>", "<EMAIL>"],
                "source": "gemini",
                "source_index": 0,
                "priority": 1
            },
            {
                "value": ["<EMAIL>", "<EMAIL>"],
                "source": "openai",
                "source_index": 0,
                "priority": 1
            }
        ]

        merged = self.merger._merge_list_field("business_email", value_list)

        # Should contain all unique emails
        assert len(merged) == 3
        assert "<EMAIL>" in merged
        assert "<EMAIL>" in merged
        assert "<EMAIL>" in merged
    
    def test_boolean_field_merging(self):
        """Test boolean field merging with priority voting"""
        # Test case where "yes" should win for jurisdiction
        value_list = [
            {"value": "yes", "priority": 1, "source": "gemini"},
            {"value": "no", "priority": 1, "source": "openai"}
        ]

        result = self.merger._merge_boolean_field("has_jurisdiction_law", value_list)
        assert result == "yes"  # Special rule: yes wins for jurisdiction

        # Test case where "no" should win due to higher total priority
        value_list = [
            {"value": "no", "priority": 1, "source": "gemini"},
            {"value": "no", "priority": 1, "source": "openai"},
            {"value": "yes", "priority": 1, "source": "openai"}
        ]

        result = self.merger._merge_boolean_field("accepts_international_orders", value_list)
        assert result == "no"
    
    def test_string_field_merging(self):
        """Test string field merging"""
        value_list = [
            {"value": "Acme Corporation", "priority": 2, "source": "gemini"},
            {"value": "ACME CORP", "priority": 1, "source": "openai"}
        ]

        result = self.merger._merge_string_field("legal_name", value_list)
        assert result == "Acme Corporation"  # Higher priority should win

    def test_legal_name_similarity_merging(self):
        """Test legal name merging with similarity checking"""
        value_list = [
            {"value": "Acme Corporation Ltd", "priority": 1},
            {"value": "ACME CORPORATION LTD", "priority": 2}  # Similar but higher priority
        ]

        result = self.merger._merge_legal_name(value_list)
        assert result == "ACME CORPORATION LTD"  # Should pick higher priority similar name
    
    def test_empty_results(self):
        """Test handling of empty results"""
        merged = self.merger.merge_multiple_results([])
        assert merged == {}
    
    def test_error_handling(self):
        """Test handling of results with errors"""
        results = [
            {
                "data": {"error": "API failed"},
                "source": "gemini",
                "source_index": 0
            },
            {
                "data": {
                    "legal_name": "Valid Corp",
                    "business_email": ["<EMAIL>"]
                },
                "source": "openai",
                "source_index": 0
            }
        ]
        
        merged = self.merger.merge_multiple_results(results)
        
        # Should only contain valid data, no error field
        assert "error" not in merged
        assert merged["legal_name"] == "Valid Corp"
        assert merged["business_email"] == ["<EMAIL>"]


if __name__ == "__main__":
    pytest.main([__file__])
