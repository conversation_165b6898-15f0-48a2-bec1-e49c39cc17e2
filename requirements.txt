# Core FastAPI and web framework dependencies
fastapi==0.115.7
uvicorn==0.34.0
starlette==0.45.3

# Database dependencies
SQLAlchemy==2.0.37
sqlmodel==0.0.22
mysql-connector-python==9.2.0
PyMySQL==1.1.1

# AI/ML dependencies for Entity Extraction
openai==1.60.0
tiktoken==0.8.0

# Google Gemini dependencies
google-generativeai==0.8.3
google-api-core==2.24.1
google-auth==2.38.0
cachetools==5.5.1
pyasn1==0.6.1
pyasn1_modules==0.4.1
rsa==4.9
proto-plus==1.26.0
protobuf==5.29.3
googleapis-common-protos==1.66.0

# Web scraping and content processing
beautifulsoup4==4.13.3
bs4==0.0.2
playwright==1.49.1
requests==2.32.3
soupsieve==2.6
tldextract==5.1.1

# HTTP client dependencies
httpx==0.28.1
httpcore==1.0.7
h11==0.14.0
anyio==4.8.0
sniffio==1.3.1
certifi==2024.12.14

# Data processing
pandas==2.2.3
numpy==2.2.2
python-dateutil==2.9.0.post0
pytz==2024.2
tzdata==2025.1

# Configuration and environment
pydantic==2.10.5
pydantic-settings==2.7.1
pydantic_core==2.27.2
python-dotenv==1.0.1
PyYAML==6.0.2

# Async and networking
aiohttp==3.11.13
aiohappyeyeballs==2.4.6
aiosignal==1.3.2
frozenlist==1.5.0
multidict==6.1.0
yarl==1.18.3
propcache==0.3.0

# Utilities and support libraries
attrs==25.1.0
annotated-types==0.7.0
charset-normalizer==3.4.1
click==8.1.8
cryptography==44.0.0
distro==1.9.0
greenlet==3.1.1
idna==3.10
jiter==0.8.2
regex==2024.11.6
six==1.17.0
structlog==25.1.0
typing_extensions==4.12.2
urllib3==2.3.0

# Playwright dependencies
pyee==12.0.0

# System monitoring (minimal)
psutil>=5.9.8
