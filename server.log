nohup: ignoring input
INFO:     Started server process [57284]
INFO:     Waiting for application startup.
2025-07-31 18:13:52,367 - app.main - INFO - Initializing Entity Extraction API
2025-07-31 18:13:52,811 INFO sqlalchemy.engine.Engine SELECT DATABASE()
2025-07-31 18:13:52,811 - sqlalchemy.engine.Engine - INFO - SELECT DATABASE()
2025-07-31 18:13:52,811 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-07-31 18:13:52,811 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-07-31 18:13:52,934 INFO sqlalchemy.engine.Engine SELECT @@sql_mode
2025-07-31 18:13:52,934 - sqlalchemy.engine.Engine - INFO - SELECT @@sql_mode
2025-07-31 18:13:52,934 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-07-31 18:13:52,934 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-07-31 18:13:52,991 INFO sqlalchemy.engine.Engine SELECT @@lower_case_table_names
2025-07-31 18:13:52,991 - sqlalchemy.engine.Engine - INFO - SELECT @@lower_case_table_names
2025-07-31 18:13:52,991 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-07-31 18:13:52,991 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-07-31 18:13:53,116 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-31 18:13:53,116 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-31 18:13:53,116 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`website_urls_gemini`
2025-07-31 18:13:53,116 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`website_urls_gemini`
2025-07-31 18:13:53,116 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-07-31 18:13:53,116 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-07-31 18:13:53,181 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`general_logs_gemini`
2025-07-31 18:13:53,181 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`general_logs_gemini`
2025-07-31 18:13:53,181 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-07-31 18:13:53,181 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-07-31 18:13:53,230 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`mcc_url_classification_gemini`
2025-07-31 18:13:53,230 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`mcc_url_classification_gemini`
2025-07-31 18:13:53,230 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-07-31 18:13:53,230 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-07-31 18:13:53,288 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`policy_url_classification_gemini`
2025-07-31 18:13:53,288 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`policy_url_classification_gemini`
2025-07-31 18:13:53,288 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-07-31 18:13:53,288 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-07-31 18:13:53,337 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`policy_analysis_new_gemini`
2025-07-31 18:13:53,337 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`policy_analysis_new_gemini`
2025-07-31 18:13:53,337 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-07-31 18:13:53,337 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-07-31 18:13:53,396 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`entity_extraction_analysis`
2025-07-31 18:13:53,396 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`entity_extraction_analysis`
2025-07-31 18:13:53,396 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-07-31 18:13:53,396 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-07-31 18:13:53,450 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`entity_extraction_url_analysis`
2025-07-31 18:13:53,450 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`entity_extraction_url_analysis`
2025-07-31 18:13:53,450 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-07-31 18:13:53,450 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-07-31 18:13:53,513 INFO sqlalchemy.engine.Engine COMMIT
2025-07-31 18:13:53,513 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-31 18:13:53,630 - app.main - INFO - Database initialized successfully
Creating Entity Extractor database tables...
2025-07-31 18:13:53,691 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-31 18:13:53,691 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-31 18:13:53,691 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`website_urls_gemini`
2025-07-31 18:13:53,691 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`website_urls_gemini`
2025-07-31 18:13:53,691 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-07-31 18:13:53,691 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-07-31 18:13:53,752 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`general_logs_gemini`
2025-07-31 18:13:53,752 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`general_logs_gemini`
2025-07-31 18:13:53,753 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-07-31 18:13:53,753 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-07-31 18:13:53,808 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`mcc_url_classification_gemini`
2025-07-31 18:13:53,808 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`mcc_url_classification_gemini`
2025-07-31 18:13:53,808 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-07-31 18:13:53,808 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-07-31 18:13:53,863 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`policy_url_classification_gemini`
2025-07-31 18:13:53,863 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`policy_url_classification_gemini`
2025-07-31 18:13:53,863 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-07-31 18:13:53,863 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-07-31 18:13:53,916 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`policy_analysis_new_gemini`
2025-07-31 18:13:53,916 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`policy_analysis_new_gemini`
2025-07-31 18:13:53,916 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-07-31 18:13:53,916 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-07-31 18:13:53,994 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`entity_extraction_analysis`
2025-07-31 18:13:53,994 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`entity_extraction_analysis`
2025-07-31 18:13:53,994 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-07-31 18:13:53,994 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-07-31 18:13:54,068 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`entity_extraction_url_analysis`
2025-07-31 18:13:54,068 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`entity_extraction_url_analysis`
2025-07-31 18:13:54,068 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-07-31 18:13:54,068 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-07-31 18:13:54,128 INFO sqlalchemy.engine.Engine COMMIT
2025-07-31 18:13:54,128 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-31 18:13:54,251 - app.main - INFO - Entity Extractor tables initialized successfully
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:8000 (Press CTRL+C to quit)
✅ Entity Extractor database tables created successfully!
Tables created:
  - entity_extraction_analysis
  - entity_extraction_url_analysis
INFO:     127.0.0.1:50626 - "GET / HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50636 - "GET /favicon.ico HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50626 - "GET /docs HTTP/1.1" 200 OK
INFO:     127.0.0.1:50626 - "GET /openapi.json HTTP/1.1" 200 OK
[2025-07-31 18:15:24][EntityExtractor][orchestrator_8c263e07-04a6-4acc-8a81-17bf2e39636a][8c263e07-04a6-4acc-8a81-17bf2e39636a] INFO: Starting simplified entity extraction orchestration
{
  "scrape_request_ref_id": "8c263e07-04a6-4acc-8a81-17bf2e39636a",
  "website_url": "https://www.houseofaks.in/",
  "org_id": "default"
}
2025-07-31 18:15:24,271 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-31 18:15:24,271 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-31 18:15:24,273 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-07-31 18:15:24,273 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-07-31 18:15:24,273 INFO sqlalchemy.engine.Engine [generated in 0.00022s] {'analysis_id': 0, 'timestamp': '2025-07-31T18:15:24.197208', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Starting simplified entity extraction orchestration", "scrape_request_ref_id": "8c263e07-04a6-4acc-8a81-17bf2e39636a",  ... (34 characters truncated) ... 197200", "data": {"scrape_request_ref_id": "8c263e07-04a6-4acc-8a81-17bf2e39636a", "website_url": "https://www.houseofaks.in/", "org_id": "default"}}', 'response': '', 'org_id': 'default'}
2025-07-31 18:15:24,273 - sqlalchemy.engine.Engine - INFO - [generated in 0.00022s] {'analysis_id': 0, 'timestamp': '2025-07-31T18:15:24.197208', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Starting simplified entity extraction orchestration", "scrape_request_ref_id": "8c263e07-04a6-4acc-8a81-17bf2e39636a",  ... (34 characters truncated) ... 197200", "data": {"scrape_request_ref_id": "8c263e07-04a6-4acc-8a81-17bf2e39636a", "website_url": "https://www.houseofaks.in/", "org_id": "default"}}', 'response': '', 'org_id': 'default'}
2025-07-31 18:15:24,341 INFO sqlalchemy.engine.Engine COMMIT
2025-07-31 18:15:24,341 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-31 18:15:24,511 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-31 18:15:24,511 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-31 18:15:24,514 INFO sqlalchemy.engine.Engine SELECT entity_extraction_analysis.id, entity_extraction_analysis.scrape_request_ref_id, entity_extraction_analysis.website_url, entity_extraction_analysis.processing_status, entity_extraction_analysis.legal_name, entity_extraction_analysis.business_email, entity_extraction_analysis.support_email, entity_extraction_analysis.business_contact_numbers, entity_extraction_analysis.business_location, entity_extraction_analysis.has_jurisdiction_law, entity_extraction_analysis.jurisdiction_details, entity_extraction_analysis.accepts_international_orders, entity_extraction_analysis.shipping_policy_details, entity_extraction_analysis.jurisdiction_place, entity_extraction_analysis.shipping_countries, entity_extraction_analysis.privacy_policy_text, entity_extraction_analysis.terms_conditions_text, entity_extraction_analysis.urls_reachable_by_gemini, entity_extraction_analysis.urls_not_reachable_by_gemini, entity_extraction_analysis.extraction_method, entity_extraction_analysis.total_urls_processed, entity_extraction_analysis.all_urls_found, entity_extraction_analysis.reachable_urls, entity_extraction_analysis.unreachable_urls, entity_extraction_analysis.policy_urls_matched, entity_extraction_analysis.created_at, entity_extraction_analysis.started_at, entity_extraction_analysis.completed_at, entity_extraction_analysis.error_message, entity_extraction_analysis.org_id 
FROM entity_extraction_analysis 
WHERE entity_extraction_analysis.scrape_request_ref_id = %(scrape_request_ref_id_1)s AND entity_extraction_analysis.org_id = %(org_id_1)s ORDER BY entity_extraction_analysis.id DESC
2025-07-31 18:15:24,514 - sqlalchemy.engine.Engine - INFO - SELECT entity_extraction_analysis.id, entity_extraction_analysis.scrape_request_ref_id, entity_extraction_analysis.website_url, entity_extraction_analysis.processing_status, entity_extraction_analysis.legal_name, entity_extraction_analysis.business_email, entity_extraction_analysis.support_email, entity_extraction_analysis.business_contact_numbers, entity_extraction_analysis.business_location, entity_extraction_analysis.has_jurisdiction_law, entity_extraction_analysis.jurisdiction_details, entity_extraction_analysis.accepts_international_orders, entity_extraction_analysis.shipping_policy_details, entity_extraction_analysis.jurisdiction_place, entity_extraction_analysis.shipping_countries, entity_extraction_analysis.privacy_policy_text, entity_extraction_analysis.terms_conditions_text, entity_extraction_analysis.urls_reachable_by_gemini, entity_extraction_analysis.urls_not_reachable_by_gemini, entity_extraction_analysis.extraction_method, entity_extraction_analysis.total_urls_processed, entity_extraction_analysis.all_urls_found, entity_extraction_analysis.reachable_urls, entity_extraction_analysis.unreachable_urls, entity_extraction_analysis.policy_urls_matched, entity_extraction_analysis.created_at, entity_extraction_analysis.started_at, entity_extraction_analysis.completed_at, entity_extraction_analysis.error_message, entity_extraction_analysis.org_id 
FROM entity_extraction_analysis 
WHERE entity_extraction_analysis.scrape_request_ref_id = %(scrape_request_ref_id_1)s AND entity_extraction_analysis.org_id = %(org_id_1)s ORDER BY entity_extraction_analysis.id DESC
2025-07-31 18:15:24,515 INFO sqlalchemy.engine.Engine [generated in 0.00021s] {'scrape_request_ref_id_1': '8c263e07-04a6-4acc-8a81-17bf2e39636a', 'org_id_1': 'default'}
2025-07-31 18:15:24,515 - sqlalchemy.engine.Engine - INFO - [generated in 0.00021s] {'scrape_request_ref_id_1': '8c263e07-04a6-4acc-8a81-17bf2e39636a', 'org_id_1': 'default'}
[2025-07-31 18:15:24][EntityExtractor][orchestrator_8c263e07-04a6-4acc-8a81-17bf2e39636a][8c263e07-04a6-4acc-8a81-17bf2e39636a] INFO: No existing analysis found
2025-07-31 18:15:24,972 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-31 18:15:24,972 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-31 18:15:24,972 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-07-31 18:15:24,972 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-07-31 18:15:24,972 INFO sqlalchemy.engine.Engine [cached since 0.6995s ago] {'analysis_id': 0, 'timestamp': '2025-07-31T18:15:24.613222', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "No existing analysis found", "scrape_request_ref_id": "8c263e07-04a6-4acc-8a81-17bf2e39636a", "timestamp": "2025-07-31T18:15:24.613212", "data": {}}', 'response': '', 'org_id': 'default'}
2025-07-31 18:15:24,972 - sqlalchemy.engine.Engine - INFO - [cached since 0.6995s ago] {'analysis_id': 0, 'timestamp': '2025-07-31T18:15:24.613222', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "No existing analysis found", "scrape_request_ref_id": "8c263e07-04a6-4acc-8a81-17bf2e39636a", "timestamp": "2025-07-31T18:15:24.613212", "data": {}}', 'response': '', 'org_id': 'default'}
2025-07-31 18:15:25,032 INFO sqlalchemy.engine.Engine COMMIT
2025-07-31 18:15:25,032 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-31 18:15:25,167 INFO sqlalchemy.engine.Engine ROLLBACK
2025-07-31 18:15:25,167 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-31 18:15:25,351 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-31 18:15:25,351 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-31 18:15:25,351 INFO sqlalchemy.engine.Engine SELECT entity_extraction_analysis.id, entity_extraction_analysis.scrape_request_ref_id, entity_extraction_analysis.website_url, entity_extraction_analysis.processing_status, entity_extraction_analysis.legal_name, entity_extraction_analysis.business_email, entity_extraction_analysis.support_email, entity_extraction_analysis.business_contact_numbers, entity_extraction_analysis.business_location, entity_extraction_analysis.has_jurisdiction_law, entity_extraction_analysis.jurisdiction_details, entity_extraction_analysis.accepts_international_orders, entity_extraction_analysis.shipping_policy_details, entity_extraction_analysis.jurisdiction_place, entity_extraction_analysis.shipping_countries, entity_extraction_analysis.privacy_policy_text, entity_extraction_analysis.terms_conditions_text, entity_extraction_analysis.urls_reachable_by_gemini, entity_extraction_analysis.urls_not_reachable_by_gemini, entity_extraction_analysis.extraction_method, entity_extraction_analysis.total_urls_processed, entity_extraction_analysis.all_urls_found, entity_extraction_analysis.reachable_urls, entity_extraction_analysis.unreachable_urls, entity_extraction_analysis.policy_urls_matched, entity_extraction_analysis.created_at, entity_extraction_analysis.started_at, entity_extraction_analysis.completed_at, entity_extraction_analysis.error_message, entity_extraction_analysis.org_id 
FROM entity_extraction_analysis 
WHERE entity_extraction_analysis.scrape_request_ref_id = %(scrape_request_ref_id_1)s AND entity_extraction_analysis.org_id = %(org_id_1)s ORDER BY entity_extraction_analysis.id DESC
2025-07-31 18:15:25,351 - sqlalchemy.engine.Engine - INFO - SELECT entity_extraction_analysis.id, entity_extraction_analysis.scrape_request_ref_id, entity_extraction_analysis.website_url, entity_extraction_analysis.processing_status, entity_extraction_analysis.legal_name, entity_extraction_analysis.business_email, entity_extraction_analysis.support_email, entity_extraction_analysis.business_contact_numbers, entity_extraction_analysis.business_location, entity_extraction_analysis.has_jurisdiction_law, entity_extraction_analysis.jurisdiction_details, entity_extraction_analysis.accepts_international_orders, entity_extraction_analysis.shipping_policy_details, entity_extraction_analysis.jurisdiction_place, entity_extraction_analysis.shipping_countries, entity_extraction_analysis.privacy_policy_text, entity_extraction_analysis.terms_conditions_text, entity_extraction_analysis.urls_reachable_by_gemini, entity_extraction_analysis.urls_not_reachable_by_gemini, entity_extraction_analysis.extraction_method, entity_extraction_analysis.total_urls_processed, entity_extraction_analysis.all_urls_found, entity_extraction_analysis.reachable_urls, entity_extraction_analysis.unreachable_urls, entity_extraction_analysis.policy_urls_matched, entity_extraction_analysis.created_at, entity_extraction_analysis.started_at, entity_extraction_analysis.completed_at, entity_extraction_analysis.error_message, entity_extraction_analysis.org_id 
FROM entity_extraction_analysis 
WHERE entity_extraction_analysis.scrape_request_ref_id = %(scrape_request_ref_id_1)s AND entity_extraction_analysis.org_id = %(org_id_1)s ORDER BY entity_extraction_analysis.id DESC
2025-07-31 18:15:25,351 INFO sqlalchemy.engine.Engine [cached since 0.8367s ago] {'scrape_request_ref_id_1': '8c263e07-04a6-4acc-8a81-17bf2e39636a', 'org_id_1': 'default'}
2025-07-31 18:15:25,351 - sqlalchemy.engine.Engine - INFO - [cached since 0.8367s ago] {'scrape_request_ref_id_1': '8c263e07-04a6-4acc-8a81-17bf2e39636a', 'org_id_1': 'default'}
2025-07-31 18:15:25,431 INFO sqlalchemy.engine.Engine INSERT INTO entity_extraction_analysis (scrape_request_ref_id, website_url, processing_status, legal_name, business_email, support_email, business_contact_numbers, business_location, has_jurisdiction_law, jurisdiction_details, accepts_international_orders, shipping_policy_details, jurisdiction_place, shipping_countries, privacy_policy_text, terms_conditions_text, urls_reachable_by_gemini, urls_not_reachable_by_gemini, extraction_method, total_urls_processed, all_urls_found, reachable_urls, unreachable_urls, policy_urls_matched, created_at, started_at, completed_at, error_message, org_id) VALUES (%(scrape_request_ref_id)s, %(website_url)s, %(processing_status)s, %(legal_name)s, %(business_email)s, %(support_email)s, %(business_contact_numbers)s, %(business_location)s, %(has_jurisdiction_law)s, %(jurisdiction_details)s, %(accepts_international_orders)s, %(shipping_policy_details)s, %(jurisdiction_place)s, %(shipping_countries)s, %(privacy_policy_text)s, %(terms_conditions_text)s, %(urls_reachable_by_gemini)s, %(urls_not_reachable_by_gemini)s, %(extraction_method)s, %(total_urls_processed)s, %(all_urls_found)s, %(reachable_urls)s, %(unreachable_urls)s, %(policy_urls_matched)s, %(created_at)s, %(started_at)s, %(completed_at)s, %(error_message)s, %(org_id)s)
2025-07-31 18:15:25,431 - sqlalchemy.engine.Engine - INFO - INSERT INTO entity_extraction_analysis (scrape_request_ref_id, website_url, processing_status, legal_name, business_email, support_email, business_contact_numbers, business_location, has_jurisdiction_law, jurisdiction_details, accepts_international_orders, shipping_policy_details, jurisdiction_place, shipping_countries, privacy_policy_text, terms_conditions_text, urls_reachable_by_gemini, urls_not_reachable_by_gemini, extraction_method, total_urls_processed, all_urls_found, reachable_urls, unreachable_urls, policy_urls_matched, created_at, started_at, completed_at, error_message, org_id) VALUES (%(scrape_request_ref_id)s, %(website_url)s, %(processing_status)s, %(legal_name)s, %(business_email)s, %(support_email)s, %(business_contact_numbers)s, %(business_location)s, %(has_jurisdiction_law)s, %(jurisdiction_details)s, %(accepts_international_orders)s, %(shipping_policy_details)s, %(jurisdiction_place)s, %(shipping_countries)s, %(privacy_policy_text)s, %(terms_conditions_text)s, %(urls_reachable_by_gemini)s, %(urls_not_reachable_by_gemini)s, %(extraction_method)s, %(total_urls_processed)s, %(all_urls_found)s, %(reachable_urls)s, %(unreachable_urls)s, %(policy_urls_matched)s, %(created_at)s, %(started_at)s, %(completed_at)s, %(error_message)s, %(org_id)s)
2025-07-31 18:15:25,431 INFO sqlalchemy.engine.Engine [generated in 0.00026s] {'scrape_request_ref_id': '8c263e07-04a6-4acc-8a81-17bf2e39636a', 'website_url': 'https://www.houseofaks.in/', 'processing_status': 'PENDING', 'legal_name': None, 'business_email': None, 'support_email': None, 'business_contact_numbers': None, 'business_location': None, 'has_jurisdiction_law': None, 'jurisdiction_details': None, 'accepts_international_orders': None, 'shipping_policy_details': None, 'jurisdiction_place': None, 'shipping_countries': None, 'privacy_policy_text': None, 'terms_conditions_text': None, 'urls_reachable_by_gemini': None, 'urls_not_reachable_by_gemini': None, 'extraction_method': None, 'total_urls_processed': None, 'all_urls_found': None, 'reachable_urls': None, 'unreachable_urls': None, 'policy_urls_matched': None, 'created_at': '2025-07-31T18:15:25.429952Z', 'started_at': None, 'completed_at': None, 'error_message': None, 'org_id': 'default'}
2025-07-31 18:15:25,431 - sqlalchemy.engine.Engine - INFO - [generated in 0.00026s] {'scrape_request_ref_id': '8c263e07-04a6-4acc-8a81-17bf2e39636a', 'website_url': 'https://www.houseofaks.in/', 'processing_status': 'PENDING', 'legal_name': None, 'business_email': None, 'support_email': None, 'business_contact_numbers': None, 'business_location': None, 'has_jurisdiction_law': None, 'jurisdiction_details': None, 'accepts_international_orders': None, 'shipping_policy_details': None, 'jurisdiction_place': None, 'shipping_countries': None, 'privacy_policy_text': None, 'terms_conditions_text': None, 'urls_reachable_by_gemini': None, 'urls_not_reachable_by_gemini': None, 'extraction_method': None, 'total_urls_processed': None, 'all_urls_found': None, 'reachable_urls': None, 'unreachable_urls': None, 'policy_urls_matched': None, 'created_at': '2025-07-31T18:15:25.429952Z', 'started_at': None, 'completed_at': None, 'error_message': None, 'org_id': 'default'}
2025-07-31 18:15:25,502 INFO sqlalchemy.engine.Engine COMMIT
2025-07-31 18:15:25,502 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-31 18:15:25,753 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-31 18:15:25,753 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-31 18:15:25,755 INFO sqlalchemy.engine.Engine SELECT entity_extraction_analysis.id, entity_extraction_analysis.scrape_request_ref_id, entity_extraction_analysis.website_url, entity_extraction_analysis.processing_status, entity_extraction_analysis.legal_name, entity_extraction_analysis.business_email, entity_extraction_analysis.support_email, entity_extraction_analysis.business_contact_numbers, entity_extraction_analysis.business_location, entity_extraction_analysis.has_jurisdiction_law, entity_extraction_analysis.jurisdiction_details, entity_extraction_analysis.accepts_international_orders, entity_extraction_analysis.shipping_policy_details, entity_extraction_analysis.jurisdiction_place, entity_extraction_analysis.shipping_countries, entity_extraction_analysis.privacy_policy_text, entity_extraction_analysis.terms_conditions_text, entity_extraction_analysis.urls_reachable_by_gemini, entity_extraction_analysis.urls_not_reachable_by_gemini, entity_extraction_analysis.extraction_method, entity_extraction_analysis.total_urls_processed, entity_extraction_analysis.all_urls_found, entity_extraction_analysis.reachable_urls, entity_extraction_analysis.unreachable_urls, entity_extraction_analysis.policy_urls_matched, entity_extraction_analysis.created_at, entity_extraction_analysis.started_at, entity_extraction_analysis.completed_at, entity_extraction_analysis.error_message, entity_extraction_analysis.org_id 
FROM entity_extraction_analysis 
WHERE entity_extraction_analysis.id = %(pk_1)s
2025-07-31 18:15:25,755 - sqlalchemy.engine.Engine - INFO - SELECT entity_extraction_analysis.id, entity_extraction_analysis.scrape_request_ref_id, entity_extraction_analysis.website_url, entity_extraction_analysis.processing_status, entity_extraction_analysis.legal_name, entity_extraction_analysis.business_email, entity_extraction_analysis.support_email, entity_extraction_analysis.business_contact_numbers, entity_extraction_analysis.business_location, entity_extraction_analysis.has_jurisdiction_law, entity_extraction_analysis.jurisdiction_details, entity_extraction_analysis.accepts_international_orders, entity_extraction_analysis.shipping_policy_details, entity_extraction_analysis.jurisdiction_place, entity_extraction_analysis.shipping_countries, entity_extraction_analysis.privacy_policy_text, entity_extraction_analysis.terms_conditions_text, entity_extraction_analysis.urls_reachable_by_gemini, entity_extraction_analysis.urls_not_reachable_by_gemini, entity_extraction_analysis.extraction_method, entity_extraction_analysis.total_urls_processed, entity_extraction_analysis.all_urls_found, entity_extraction_analysis.reachable_urls, entity_extraction_analysis.unreachable_urls, entity_extraction_analysis.policy_urls_matched, entity_extraction_analysis.created_at, entity_extraction_analysis.started_at, entity_extraction_analysis.completed_at, entity_extraction_analysis.error_message, entity_extraction_analysis.org_id 
FROM entity_extraction_analysis 
WHERE entity_extraction_analysis.id = %(pk_1)s
2025-07-31 18:15:25,755 INFO sqlalchemy.engine.Engine [generated in 0.00014s] {'pk_1': 130}
2025-07-31 18:15:25,755 - sqlalchemy.engine.Engine - INFO - [generated in 0.00014s] {'pk_1': 130}
[2025-07-31 18:15:25][EntityExtractor][orchestrator_8c263e07-04a6-4acc-8a81-17bf2e39636a][8c263e07-04a6-4acc-8a81-17bf2e39636a] INFO: Created new analysis record with ID: 130
2025-07-31 18:15:25,891 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-31 18:15:25,891 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-31 18:15:25,891 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-07-31 18:15:25,891 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-07-31 18:15:25,891 INFO sqlalchemy.engine.Engine [cached since 1.619s ago] {'analysis_id': 0, 'timestamp': '2025-07-31T18:15:25.833369', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Created new analysis record with ID: 130", "scrape_request_ref_id": "8c263e07-04a6-4acc-8a81-17bf2e39636a", "timestamp": "2025-07-31T18:15:25.833362", "data": {}}', 'response': '', 'org_id': 'default'}
2025-07-31 18:15:25,891 - sqlalchemy.engine.Engine - INFO - [cached since 1.619s ago] {'analysis_id': 0, 'timestamp': '2025-07-31T18:15:25.833369', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Created new analysis record with ID: 130", "scrape_request_ref_id": "8c263e07-04a6-4acc-8a81-17bf2e39636a", "timestamp": "2025-07-31T18:15:25.833362", "data": {}}', 'response': '', 'org_id': 'default'}
2025-07-31 18:15:25,972 INFO sqlalchemy.engine.Engine COMMIT
2025-07-31 18:15:25,972 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-31 18:15:26,093 INFO sqlalchemy.engine.Engine ROLLBACK
2025-07-31 18:15:26,093 - sqlalchemy.engine.Engine - INFO - ROLLBACK
[2025-07-31 18:15:26][EntityExtractor][130][8c263e07-04a6-4acc-8a81-17bf2e39636a] INFO: Updated logger with analysis ID: 130
2025-07-31 18:15:26,342 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-31 18:15:26,342 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-31 18:15:26,342 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-07-31 18:15:26,342 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-07-31 18:15:26,342 INFO sqlalchemy.engine.Engine [cached since 2.07s ago] {'analysis_id': 130, 'timestamp': '2025-07-31T18:15:26.221776', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Updated logger with analysis ID: 130", "scrape_request_ref_id": "8c263e07-04a6-4acc-8a81-17bf2e39636a", "timestamp": "2025-07-31T18:15:26.221767", "data": {}}', 'response': '', 'org_id': 'default'}
2025-07-31 18:15:26,342 - sqlalchemy.engine.Engine - INFO - [cached since 2.07s ago] {'analysis_id': 130, 'timestamp': '2025-07-31T18:15:26.221776', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Updated logger with analysis ID: 130", "scrape_request_ref_id": "8c263e07-04a6-4acc-8a81-17bf2e39636a", "timestamp": "2025-07-31T18:15:26.221767", "data": {}}', 'response': '', 'org_id': 'default'}
2025-07-31 18:15:26,431 INFO sqlalchemy.engine.Engine COMMIT
2025-07-31 18:15:26,431 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-31 18:15:26,605 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-31 18:15:26,605 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-31 18:15:26,607 INFO sqlalchemy.engine.Engine SELECT entity_extraction_analysis.id AS entity_extraction_analysis_id, entity_extraction_analysis.scrape_request_ref_id AS entity_extraction_analysis_scrape_request_ref_id, entity_extraction_analysis.website_url AS entity_extraction_analysis_website_url, entity_extraction_analysis.processing_status AS entity_extraction_analysis_processing_status, entity_extraction_analysis.legal_name AS entity_extraction_analysis_legal_name, entity_extraction_analysis.business_email AS entity_extraction_analysis_business_email, entity_extraction_analysis.support_email AS entity_extraction_analysis_support_email, entity_extraction_analysis.business_contact_numbers AS entity_extraction_analysis_business_contact_numbers, entity_extraction_analysis.business_location AS entity_extraction_analysis_business_location, entity_extraction_analysis.has_jurisdiction_law AS entity_extraction_analysis_has_jurisdiction_law, entity_extraction_analysis.jurisdiction_details AS entity_extraction_analysis_jurisdiction_details, entity_extraction_analysis.accepts_international_orders AS entity_extraction_analysis_accepts_international_orders, entity_extraction_analysis.shipping_policy_details AS entity_extraction_analysis_shipping_policy_details, entity_extraction_analysis.jurisdiction_place AS entity_extraction_analysis_jurisdiction_place, entity_extraction_analysis.shipping_countries AS entity_extraction_analysis_shipping_countries, entity_extraction_analysis.privacy_policy_text AS entity_extraction_analysis_privacy_policy_text, entity_extraction_analysis.terms_conditions_text AS entity_extraction_analysis_terms_conditions_text, entity_extraction_analysis.urls_reachable_by_gemini AS entity_extraction_analysis_urls_reachable_by_gemini, entity_extraction_analysis.urls_not_reachable_by_gemini AS entity_extraction_analysis_urls_not_reachable_by_gemini, entity_extraction_analysis.extraction_method AS entity_extraction_analysis_extraction_method, entity_extraction_analysis.total_urls_processed AS entity_extraction_analysis_total_urls_processed, entity_extraction_analysis.all_urls_found AS entity_extraction_analysis_all_urls_found, entity_extraction_analysis.reachable_urls AS entity_extraction_analysis_reachable_urls, entity_extraction_analysis.unreachable_urls AS entity_extraction_analysis_unreachable_urls, entity_extraction_analysis.policy_urls_matched AS entity_extraction_analysis_policy_urls_matched, entity_extraction_analysis.created_at AS entity_extraction_analysis_created_at, entity_extraction_analysis.started_at AS entity_extraction_analysis_started_at, entity_extraction_analysis.completed_at AS entity_extraction_analysis_completed_at, entity_extraction_analysis.error_message AS entity_extraction_analysis_error_message, entity_extraction_analysis.org_id AS entity_extraction_analysis_org_id 
FROM entity_extraction_analysis 
WHERE entity_extraction_analysis.id = %(pk_1)s
2025-07-31 18:15:26,607 - sqlalchemy.engine.Engine - INFO - SELECT entity_extraction_analysis.id AS entity_extraction_analysis_id, entity_extraction_analysis.scrape_request_ref_id AS entity_extraction_analysis_scrape_request_ref_id, entity_extraction_analysis.website_url AS entity_extraction_analysis_website_url, entity_extraction_analysis.processing_status AS entity_extraction_analysis_processing_status, entity_extraction_analysis.legal_name AS entity_extraction_analysis_legal_name, entity_extraction_analysis.business_email AS entity_extraction_analysis_business_email, entity_extraction_analysis.support_email AS entity_extraction_analysis_support_email, entity_extraction_analysis.business_contact_numbers AS entity_extraction_analysis_business_contact_numbers, entity_extraction_analysis.business_location AS entity_extraction_analysis_business_location, entity_extraction_analysis.has_jurisdiction_law AS entity_extraction_analysis_has_jurisdiction_law, entity_extraction_analysis.jurisdiction_details AS entity_extraction_analysis_jurisdiction_details, entity_extraction_analysis.accepts_international_orders AS entity_extraction_analysis_accepts_international_orders, entity_extraction_analysis.shipping_policy_details AS entity_extraction_analysis_shipping_policy_details, entity_extraction_analysis.jurisdiction_place AS entity_extraction_analysis_jurisdiction_place, entity_extraction_analysis.shipping_countries AS entity_extraction_analysis_shipping_countries, entity_extraction_analysis.privacy_policy_text AS entity_extraction_analysis_privacy_policy_text, entity_extraction_analysis.terms_conditions_text AS entity_extraction_analysis_terms_conditions_text, entity_extraction_analysis.urls_reachable_by_gemini AS entity_extraction_analysis_urls_reachable_by_gemini, entity_extraction_analysis.urls_not_reachable_by_gemini AS entity_extraction_analysis_urls_not_reachable_by_gemini, entity_extraction_analysis.extraction_method AS entity_extraction_analysis_extraction_method, entity_extraction_analysis.total_urls_processed AS entity_extraction_analysis_total_urls_processed, entity_extraction_analysis.all_urls_found AS entity_extraction_analysis_all_urls_found, entity_extraction_analysis.reachable_urls AS entity_extraction_analysis_reachable_urls, entity_extraction_analysis.unreachable_urls AS entity_extraction_analysis_unreachable_urls, entity_extraction_analysis.policy_urls_matched AS entity_extraction_analysis_policy_urls_matched, entity_extraction_analysis.created_at AS entity_extraction_analysis_created_at, entity_extraction_analysis.started_at AS entity_extraction_analysis_started_at, entity_extraction_analysis.completed_at AS entity_extraction_analysis_completed_at, entity_extraction_analysis.error_message AS entity_extraction_analysis_error_message, entity_extraction_analysis.org_id AS entity_extraction_analysis_org_id 
FROM entity_extraction_analysis 
WHERE entity_extraction_analysis.id = %(pk_1)s
2025-07-31 18:15:26,607 INFO sqlalchemy.engine.Engine [generated in 0.00018s] {'pk_1': 130}
2025-07-31 18:15:26,607 - sqlalchemy.engine.Engine - INFO - [generated in 0.00018s] {'pk_1': 130}
2025-07-31 18:15:26,693 INFO sqlalchemy.engine.Engine UPDATE entity_extraction_analysis SET processing_status=%(processing_status)s, started_at=%(started_at)s WHERE entity_extraction_analysis.id = %(entity_extraction_analysis_id)s
2025-07-31 18:15:26,693 - sqlalchemy.engine.Engine - INFO - UPDATE entity_extraction_analysis SET processing_status=%(processing_status)s, started_at=%(started_at)s WHERE entity_extraction_analysis.id = %(entity_extraction_analysis_id)s
2025-07-31 18:15:26,693 INFO sqlalchemy.engine.Engine [generated in 0.00016s] {'processing_status': 'IN_PROGRESS', 'started_at': '2025-07-31T18:15:26.550198', 'entity_extraction_analysis_id': 130}
2025-07-31 18:15:26,693 - sqlalchemy.engine.Engine - INFO - [generated in 0.00016s] {'processing_status': 'IN_PROGRESS', 'started_at': '2025-07-31T18:15:26.550198', 'entity_extraction_analysis_id': 130}
2025-07-31 18:15:26,751 INFO sqlalchemy.engine.Engine COMMIT
2025-07-31 18:15:26,751 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-07-31 18:15:26][EntityExtractor][130][8c263e07-04a6-4acc-8a81-17bf2e39636a] INFO: Updated analysis 130 status to IN_PROGRESS
2025-07-31 18:15:27,015 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-31 18:15:27,015 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-31 18:15:27,015 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-07-31 18:15:27,015 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-07-31 18:15:27,015 INFO sqlalchemy.engine.Engine [cached since 2.743s ago] {'analysis_id': 130, 'timestamp': '2025-07-31T18:15:26.860822', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Updated analysis 130 status to IN_PROGRESS", "scrape_request_ref_id": "8c263e07-04a6-4acc-8a81-17bf2e39636a", "timestamp": "2025-07-31T18:15:26.860814", "data": {}}', 'response': '', 'org_id': 'default'}
2025-07-31 18:15:27,015 - sqlalchemy.engine.Engine - INFO - [cached since 2.743s ago] {'analysis_id': 130, 'timestamp': '2025-07-31T18:15:26.860822', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Updated analysis 130 status to IN_PROGRESS", "scrape_request_ref_id": "8c263e07-04a6-4acc-8a81-17bf2e39636a", "timestamp": "2025-07-31T18:15:26.860814", "data": {}}', 'response': '', 'org_id': 'default'}
2025-07-31 18:15:27,223 INFO sqlalchemy.engine.Engine COMMIT
2025-07-31 18:15:27,223 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-07-31 18:15:27][EntityExtractor][130][8c263e07-04a6-4acc-8a81-17bf2e39636a] ERROR: Entity extraction failed: No module named 'Extractor'
2025-07-31 18:15:27,486 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-31 18:15:27,486 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-31 18:15:27,486 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-07-31 18:15:27,486 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-07-31 18:15:27,486 INFO sqlalchemy.engine.Engine [cached since 3.214s ago] {'analysis_id': 130, 'timestamp': '2025-07-31T18:15:27.404316', 'type': 'entity_extractor', 'messages': '{"level": "ERROR", "message": "Entity extraction failed: No module named \'Extractor\'", "scrape_request_ref_id": "8c263e07-04a6-4acc-8a81-17bf2e39636a", "timestamp": "2025-07-31T18:15:27.404305", "data": {}}', 'response': '', 'org_id': 'default'}
2025-07-31 18:15:27,486 - sqlalchemy.engine.Engine - INFO - [cached since 3.214s ago] {'analysis_id': 130, 'timestamp': '2025-07-31T18:15:27.404316', 'type': 'entity_extractor', 'messages': '{"level": "ERROR", "message": "Entity extraction failed: No module named \'Extractor\'", "scrape_request_ref_id": "8c263e07-04a6-4acc-8a81-17bf2e39636a", "timestamp": "2025-07-31T18:15:27.404305", "data": {}}', 'response': '', 'org_id': 'default'}
2025-07-31 18:15:27,551 INFO sqlalchemy.engine.Engine COMMIT
2025-07-31 18:15:27,551 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-31 18:15:27,785 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-31 18:15:27,785 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-31 18:15:27,785 INFO sqlalchemy.engine.Engine INSERT INTO entity_extraction_analysis (scrape_request_ref_id, website_url, processing_status, legal_name, business_email, support_email, business_contact_numbers, business_location, has_jurisdiction_law, jurisdiction_details, accepts_international_orders, shipping_policy_details, jurisdiction_place, shipping_countries, privacy_policy_text, terms_conditions_text, urls_reachable_by_gemini, urls_not_reachable_by_gemini, extraction_method, total_urls_processed, all_urls_found, reachable_urls, unreachable_urls, policy_urls_matched, created_at, started_at, completed_at, error_message, org_id) VALUES (%(scrape_request_ref_id)s, %(website_url)s, %(processing_status)s, %(legal_name)s, %(business_email)s, %(support_email)s, %(business_contact_numbers)s, %(business_location)s, %(has_jurisdiction_law)s, %(jurisdiction_details)s, %(accepts_international_orders)s, %(shipping_policy_details)s, %(jurisdiction_place)s, %(shipping_countries)s, %(privacy_policy_text)s, %(terms_conditions_text)s, %(urls_reachable_by_gemini)s, %(urls_not_reachable_by_gemini)s, %(extraction_method)s, %(total_urls_processed)s, %(all_urls_found)s, %(reachable_urls)s, %(unreachable_urls)s, %(policy_urls_matched)s, %(created_at)s, %(started_at)s, %(completed_at)s, %(error_message)s, %(org_id)s)
2025-07-31 18:15:27,785 - sqlalchemy.engine.Engine - INFO - INSERT INTO entity_extraction_analysis (scrape_request_ref_id, website_url, processing_status, legal_name, business_email, support_email, business_contact_numbers, business_location, has_jurisdiction_law, jurisdiction_details, accepts_international_orders, shipping_policy_details, jurisdiction_place, shipping_countries, privacy_policy_text, terms_conditions_text, urls_reachable_by_gemini, urls_not_reachable_by_gemini, extraction_method, total_urls_processed, all_urls_found, reachable_urls, unreachable_urls, policy_urls_matched, created_at, started_at, completed_at, error_message, org_id) VALUES (%(scrape_request_ref_id)s, %(website_url)s, %(processing_status)s, %(legal_name)s, %(business_email)s, %(support_email)s, %(business_contact_numbers)s, %(business_location)s, %(has_jurisdiction_law)s, %(jurisdiction_details)s, %(accepts_international_orders)s, %(shipping_policy_details)s, %(jurisdiction_place)s, %(shipping_countries)s, %(privacy_policy_text)s, %(terms_conditions_text)s, %(urls_reachable_by_gemini)s, %(urls_not_reachable_by_gemini)s, %(extraction_method)s, %(total_urls_processed)s, %(all_urls_found)s, %(reachable_urls)s, %(unreachable_urls)s, %(policy_urls_matched)s, %(created_at)s, %(started_at)s, %(completed_at)s, %(error_message)s, %(org_id)s)
2025-07-31 18:15:27,785 INFO sqlalchemy.engine.Engine [cached since 2.354s ago] {'scrape_request_ref_id': '8c263e07-04a6-4acc-8a81-17bf2e39636a', 'website_url': 'https://www.houseofaks.in/', 'processing_status': 'FAILED', 'legal_name': None, 'business_email': None, 'support_email': None, 'business_contact_numbers': None, 'business_location': None, 'has_jurisdiction_law': None, 'jurisdiction_details': None, 'accepts_international_orders': None, 'shipping_policy_details': None, 'jurisdiction_place': None, 'shipping_countries': None, 'privacy_policy_text': None, 'terms_conditions_text': None, 'urls_reachable_by_gemini': None, 'urls_not_reachable_by_gemini': None, 'extraction_method': None, 'total_urls_processed': None, 'all_urls_found': None, 'reachable_urls': None, 'unreachable_urls': None, 'policy_urls_matched': None, 'created_at': '2025-07-31T18:15:27.691662', 'started_at': '2025-07-31T18:15:27.691648', 'completed_at': None, 'error_message': "No module named 'Extractor'", 'org_id': 'default'}
2025-07-31 18:15:27,785 - sqlalchemy.engine.Engine - INFO - [cached since 2.354s ago] {'scrape_request_ref_id': '8c263e07-04a6-4acc-8a81-17bf2e39636a', 'website_url': 'https://www.houseofaks.in/', 'processing_status': 'FAILED', 'legal_name': None, 'business_email': None, 'support_email': None, 'business_contact_numbers': None, 'business_location': None, 'has_jurisdiction_law': None, 'jurisdiction_details': None, 'accepts_international_orders': None, 'shipping_policy_details': None, 'jurisdiction_place': None, 'shipping_countries': None, 'privacy_policy_text': None, 'terms_conditions_text': None, 'urls_reachable_by_gemini': None, 'urls_not_reachable_by_gemini': None, 'extraction_method': None, 'total_urls_processed': None, 'all_urls_found': None, 'reachable_urls': None, 'unreachable_urls': None, 'policy_urls_matched': None, 'created_at': '2025-07-31T18:15:27.691662', 'started_at': '2025-07-31T18:15:27.691648', 'completed_at': None, 'error_message': "No module named 'Extractor'", 'org_id': 'default'}
2025-07-31 18:15:27,847 INFO sqlalchemy.engine.Engine COMMIT
2025-07-31 18:15:27,847 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-31 18:15:28,081 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-31 18:15:28,081 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-31 18:15:28,082 INFO sqlalchemy.engine.Engine SELECT entity_extraction_analysis.id, entity_extraction_analysis.scrape_request_ref_id, entity_extraction_analysis.website_url, entity_extraction_analysis.processing_status, entity_extraction_analysis.legal_name, entity_extraction_analysis.business_email, entity_extraction_analysis.support_email, entity_extraction_analysis.business_contact_numbers, entity_extraction_analysis.business_location, entity_extraction_analysis.has_jurisdiction_law, entity_extraction_analysis.jurisdiction_details, entity_extraction_analysis.accepts_international_orders, entity_extraction_analysis.shipping_policy_details, entity_extraction_analysis.jurisdiction_place, entity_extraction_analysis.shipping_countries, entity_extraction_analysis.privacy_policy_text, entity_extraction_analysis.terms_conditions_text, entity_extraction_analysis.urls_reachable_by_gemini, entity_extraction_analysis.urls_not_reachable_by_gemini, entity_extraction_analysis.extraction_method, entity_extraction_analysis.total_urls_processed, entity_extraction_analysis.all_urls_found, entity_extraction_analysis.reachable_urls, entity_extraction_analysis.unreachable_urls, entity_extraction_analysis.policy_urls_matched, entity_extraction_analysis.created_at, entity_extraction_analysis.started_at, entity_extraction_analysis.completed_at, entity_extraction_analysis.error_message, entity_extraction_analysis.org_id 
FROM entity_extraction_analysis 
WHERE entity_extraction_analysis.id = %(pk_1)s
2025-07-31 18:15:28,082 - sqlalchemy.engine.Engine - INFO - SELECT entity_extraction_analysis.id, entity_extraction_analysis.scrape_request_ref_id, entity_extraction_analysis.website_url, entity_extraction_analysis.processing_status, entity_extraction_analysis.legal_name, entity_extraction_analysis.business_email, entity_extraction_analysis.support_email, entity_extraction_analysis.business_contact_numbers, entity_extraction_analysis.business_location, entity_extraction_analysis.has_jurisdiction_law, entity_extraction_analysis.jurisdiction_details, entity_extraction_analysis.accepts_international_orders, entity_extraction_analysis.shipping_policy_details, entity_extraction_analysis.jurisdiction_place, entity_extraction_analysis.shipping_countries, entity_extraction_analysis.privacy_policy_text, entity_extraction_analysis.terms_conditions_text, entity_extraction_analysis.urls_reachable_by_gemini, entity_extraction_analysis.urls_not_reachable_by_gemini, entity_extraction_analysis.extraction_method, entity_extraction_analysis.total_urls_processed, entity_extraction_analysis.all_urls_found, entity_extraction_analysis.reachable_urls, entity_extraction_analysis.unreachable_urls, entity_extraction_analysis.policy_urls_matched, entity_extraction_analysis.created_at, entity_extraction_analysis.started_at, entity_extraction_analysis.completed_at, entity_extraction_analysis.error_message, entity_extraction_analysis.org_id 
FROM entity_extraction_analysis 
WHERE entity_extraction_analysis.id = %(pk_1)s
2025-07-31 18:15:28,082 INFO sqlalchemy.engine.Engine [cached since 2.327s ago] {'pk_1': 131}
2025-07-31 18:15:28,082 - sqlalchemy.engine.Engine - INFO - [cached since 2.327s ago] {'pk_1': 131}
[2025-07-31 18:15:28][EntityExtractor][130][8c263e07-04a6-4acc-8a81-17bf2e39636a] INFO: Stored failed entity extraction analysis with ID: 131
2025-07-31 18:15:28,260 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-31 18:15:28,260 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-31 18:15:28,261 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-07-31 18:15:28,261 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-07-31 18:15:28,261 INFO sqlalchemy.engine.Engine [cached since 3.988s ago] {'analysis_id': 130, 'timestamp': '2025-07-31T18:15:28.182113', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Stored failed entity extraction analysis with ID: 131", "scrape_request_ref_id": "8c263e07-04a6-4acc-8a81-17bf2e39636a", "timestamp": "2025-07-31T18:15:28.182105", "data": {}}', 'response': '', 'org_id': 'default'}
2025-07-31 18:15:28,261 - sqlalchemy.engine.Engine - INFO - [cached since 3.988s ago] {'analysis_id': 130, 'timestamp': '2025-07-31T18:15:28.182113', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Stored failed entity extraction analysis with ID: 131", "scrape_request_ref_id": "8c263e07-04a6-4acc-8a81-17bf2e39636a", "timestamp": "2025-07-31T18:15:28.182105", "data": {}}', 'response': '', 'org_id': 'default'}
2025-07-31 18:15:28,326 INFO sqlalchemy.engine.Engine COMMIT
2025-07-31 18:15:28,326 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-31 18:15:28,441 INFO sqlalchemy.engine.Engine ROLLBACK
2025-07-31 18:15:28,441 - sqlalchemy.engine.Engine - INFO - ROLLBACK
INFO:     127.0.0.1:60592 - "POST /entity-extraction/analyze HTTP/1.1" 200 OK
