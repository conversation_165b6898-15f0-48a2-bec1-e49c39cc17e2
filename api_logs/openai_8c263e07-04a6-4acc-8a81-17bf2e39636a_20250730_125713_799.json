{"api_type": "openai", "timestamp": "2025-07-30T12:57:13.799603", "name": "entity_extraction", "request_id": "8c263e07-04a6-4acc-8a81-17bf2e39636a", "context": {"service": "orchestrator", "missing_field": "business_location"}, "request": {"messages": [{"role": "user", "content": "\nYou are an expert business analyst who has a lot of experience in dealing with business's official website and the detailed information present in websites URLs. \nYou will be provided with text present in either of website's terms and condition OR privacy policy OR shipping policy OR returns policy OR refund policy urls OR and you need to extract specific business information from this text.\n\nSource URL: https://houseofaks.in/pages/house-of-aks\n\n* Input Starts *\nText Content:\nHouse of AKS: Shaukeen, for the Keen Eye Skip to content Previous Free Express Shipping above 1999Free international shipping on order above 185$Enjoy 10% Off on Your First Order, Use Code: AKS10 Next New In Best Sellers Shop Kurta Sets Kurtis Kurtas Co-ord Set Bottoms Pants Gharara Sharara Palazzo Parellal Sarees Silk Scarfs Semi-Stitched or Unstitched Shop All Collections Regal Tissue Sarees Gulaabo Falak Zia Mehak collection Gauhar - Muslin Silk Edit Chaand - Festive Edit College Wardrobe Heer - Viscose Georgette Edit Office Wardrobe Aaliyah - Hues of Rose & Sky Riwaaz - Silk Sarees Edit Inaya, Nargis & Noori - The Georgette Saree Edit Ombre Cotton Influencers Silk Edit Wedding Collection Navigation houseofaks.in 0 New InBest SellersShop Kurta SetsKurtisKurtasCo-ord SetBottoms Pants Gharara Sharara Palazzo Parellal SareesSilk ScarfsSemi-Stitched or UnstitchedShop AllCollections Regal Tissue SareesGulaaboFalak ZiaMehak collectionGauhar - Muslin Silk EditChaand - Festive EditCollege WardrobeHeer - Viscose Georgette EditOffice WardrobeAaliyah - Hues of Rose & SkyRiwaaz - Silk Sarees EditInaya, Nargis & Noori - The Georgette Saree EditOmbreCotton InfluencersSilk EditWedding Collection Account View all results Cookie policyI agree to the processing of my data in accordance with the conditions set out in the policy of Privacy. Accept Decline Your is empty Start shopping Add order note Save House of AKS: Shaukeen, for the Keen Eye House of AKS: Shaukeen - For The Keen Eye\" beautifully captures the essence of business, blending tradition and elegance. It speaks to the discerning customer who appreciates the intricate artistry of Chikankari and the timeless luxury of Banarasi silk. \"Shaukeen\" reflects a connoisseur's passion for fine craftsmanship, while \"For The Keen Eye\" highlights the attention to detail and exclusivity your brand . Together, it's a promise of sophistication for those who seek beauty in every thread.Join us on a sartorial journey where each outfit is a masterpiece, meticulously designed to honour the rich cultural heritage. Let Shaukeen be your companion in celebrating the beauty of tradition with the modern flair. Free shippingFree shipping on all orders above Rs. Customer serviceContact us 24 hours a day, 7 days a week. 7 Days ReturnSimply return it within 7 days for an exchange. 100% Payment SecureWe ensure secure payment with Razorpay. Go to slide 1 Go to slide 2 Go to slide 3 Go to slide 4 Prices are displayed in your local currency for convenience. Final payment is processed in Indian Rupees ( ) at checkout. Policies Refund Policy Terms of Service Blogs Do not sell or share my personal information Info & Help FAQs About us Contact us Follow us To Our NewsletterPromotions, new products and sales. Directly to your inbox Your e-mail Country/region India (INR ) Country/region land Islands (INR ) Albania (INR ) Andorra (INR ) Australia (INR ) Austria (INR ) Belarus (INR ) Belgium (INR ) Bosnia & Herzegovina (INR ) Bulgaria (INR ) Canada (INR ) Croatia (INR ) Czechia (INR ) Denmark (INR ) Estonia (INR ) Faroe Islands (INR ) Finland (INR ) France (INR ) Germany (INR ) Gibraltar (INR ) Greece (INR ) Guernsey (INR ) Hong Kong SAR (INR ) Hungary (INR ) Iceland (INR ) India (INR ) Ireland (INR ) Isle of Man (INR ) Israel (INR ) Italy (INR ) Japan (INR ) Jersey (INR ) Kosovo (INR ) Latvia (INR ) Liechtenstein (INR ) Lithuania (INR ) Luxembourg (INR ) Malaysia (INR ) Malta (INR ) Moldova (INR ) Monaco (INR ) Montenegro (INR ) Netherlands (INR ) New Zealand (INR ) North Macedonia (INR ) Norway (INR ) Poland (INR ) Portugal (INR ) Romania (INR ) Russia (INR ) San Marino (INR ) Serbia (INR ) Singapore (INR ) Slovakia (INR ) Slovenia (INR ) South Korea (INR ) Spain (INR ) Svalbard & Jan Mayen (INR ) Sweden (INR ) Switzerland (INR ) Ukraine (INR ) United Arab Emirates (INR ) United Kingdom (INR ) United States (INR ) Vatican City (INR ) Language English Language English Fran ais houseofaks.in Shopify USE CODE 'AKS10' TO GET Flat 10% ON FIRST PURCHASE\n* Input Ends *\n\n\n* Tasks Starts *\nYou will be provided with text present in either of website's home page, terms and condition OR privacy policy OR shipping policy OR returns policy OR refund policy urls scraped text and you need to extract specific business information from this text.\nYour main task is to extract the following information from the Text Content.\n\n1. \"legal_name\" ---> Legal Name of the Company: a string, The official registered business name (single value only)\n2. \"business_email\" ---> Business Official Email Address: a list [], Primary business contact email/emails as array (contact@, info@, etc.)\n3. \"support_email\" ---> Support Emails: a list [], Customer support email/emails addresses as array (support@, help@, customerservice@, etc.)\n4. \"business_contact_numbers\" ---> Business Official Contact Numbers: a list[],  Official phone number/numbers for business inquiries as array, extract as it is\n5. \"business_location\" ---> Business Location: a list [] for addresses, Complete business addresses as array (include all locations if multiple official address is present)\n6. \"accepts_international_orders\" ---> International Shipping Offered : a string, yes or no or not_mentioned, Does the company accept international orders/shipping? (yes/no/not_mentioned). It is assumed that website offered services to Indin. Any country outside India is assumed be international.\n7. \"shipping_countries\" ---> Shipping Countries : a list [], a list of destination where the company offers its shipping services, should be specifically mentioned in the policies\n8. \"shipping_policy_details\" ---> Shipping Policy Details: a string, Summary of phrases or snippets which were used to conclude the answer on international shipping details, (not more than 10-20 words)\n9. \"has_jurisdiction_law\" ---> Jurisdiction/Governing Law: a string, yes/no, Does the website specify governing law or jurisdiction in case of any dispute (yes/no)\n10. \"jurisdiction_place\" ---> Jurisdiction/Governing Law location : a list [], this should have the places of which the governing law applies to any disputes\n11. \"jurisdiction_details\" ---> Jurisdiction/Governing Law details : a string, summary of snippet which was used to specify the jurisdiction/law related query above, in not more than 10-25 words.\n\n**Instructions:**\n\n- Go through all the text and only derive mentioned in the websites, for e.g. do not assume anything, do not read reviews mentioned, or comments in the websites mentioend. \n- Extract only verified information that appears on the website\n- If a piece of information is not found, return empty string or list for that field\n- Be precise and avoid making assumptions\n\n**Output Format:**\nReturn your response strictly in JSON format with below 11 keys:\n\n```json\n{\n    \"legal_name\": \"Single company legal name or null\", a string, provide \"\" empty string in case not identified.\n    \"business_email\": a list, [\"<EMAIL>\", \"<EMAIL>\"], provide empty [] list in case not identified,\n    \"support_email\": a list, [\"<EMAIL>\", \"<EMAIL>\"], provide empty [] list in case not identified,\n    \"business_contact_numbers\": a list, [\"+1234567890\", \"+0987654321\"], provide empty [] list in case not identified,\n    \"business_location\": a list, [\"Address 1\", \"Address 2\"], provide empty [] list in case not identified,\n    \"accepts_international_orders\": \"yes/no\", a string\n    \"shipping_countries\":[\"India\",\"Australia\"....], a list of destination where the company offers its shipping services, should be specifically mentioned in the policies, provide empty [] list in case not identified,\n    \"shipping_policy_details\": \"Summary of shipping and delivery policies or null\", a string.\n    \"has_jurisdiction_law\": \"yes/no\", Only yes and no, a string.\n    \"jurisdiction_place\": a list [], this should have the places of which the governing law applies to any disputes, provide empty [] list in case not identified,\n    \"jurisdiction_details\" : Jurisdiction/Governing Law details : a string, summary of snippet which was used to specify the jurisdiction/lar related query above, in not more than 10-15 words.\n    }\n\n```\n\nPlease ensure accuracy and only extract information that is clearly visible on the website pages.\n"}]}, "response": "ChatCompletion(id='chatcmpl-Bz0emSMxzDe3PsGDqnMzCiKVGBAcX', choices=[Choice(finish_reason='stop', index=0, logprobs=None, message=ChatCompletionMessage(content='```json\\n{\\n    \"legal_name\": \"House of AKS\",\\n    \"business_email\": [],\\n    \"support_email\": [],\\n    \"business_contact_numbers\": [],\\n    \"business_location\": [],\\n    \"accepts_international_orders\": \"yes\",\\n    \"shipping_countries\": [\"India\"],\\n    \"shipping_policy_details\": \"Free international shipping on order above 185$\",\\n    \"has_jurisdiction_law\": \"no\",\\n    \"jurisdiction_place\": [],\\n    \"jurisdiction_details\": \"\"\\n}\\n```', refusal=None, role='assistant', audio=None, function_call=None, tool_calls=None, annotations=[]))], created=1753880232, model='gpt-4o-mini-2024-07-18', object='chat.completion', service_tier='default', system_fingerprint='fp_34a54ae93c', usage=CompletionUsage(completion_tokens=105, prompt_tokens=2072, total_tokens=2177, completion_tokens_details=CompletionTokensDetails(accepted_prediction_tokens=0, audio_tokens=0, reasoning_tokens=0, rejected_prediction_tokens=0), prompt_tokens_details=PromptTokensDetails(audio_tokens=0, cached_tokens=2048)))"}